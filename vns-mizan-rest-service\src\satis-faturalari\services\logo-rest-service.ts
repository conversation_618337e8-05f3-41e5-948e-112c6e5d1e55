import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import { consola } from 'consola'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'

/**
 * Service for handling Logo REST API operations for Satis Faturalari
 */
const SatisFaturalariLogoRestService = {
  /**
   * Gets an access token from the Logo REST API.
   * If logoCredentials is provided, it will use those credentials instead of the ones in the config.
   */
  getToken: async ({
    veritabaniId,
    logoCredentials
  }: {
    veritabaniId: string,
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const restApiUrl = logoConfig.erp.rest_settings.rest_api_url
    const username = logoCredentials?.kullanici_adi || logoConfig.erp.kullanici_adi
    const password = logoCredentials?.sifre || logoConfig.erp.sifre
    const firmno = logoConfig.erp.firma_numarasi
    const clientKey = logoConfig.erp.rest_settings.client_key

    if (!restApiUrl || !username || !password || !firmno || !clientKey) {
      throw new Error(`Logo REST API yapılandırması eksik, veritabanı ID: ${veritabaniId}`)
    }

    try {
      consola.info(`Logo erişim anahtarı isteniyor: ${restApiUrl}/token`)
      const response = await fetch(`${restApiUrl}/token`, {
        method: 'POST',
        body: `grant_type=password&username=${username}&firmno=${firmno}&password=${password}`,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'Authorization': `Basic ${clientKey}` },
      })
      const data = (await response.json()) as { access_token?: string, error?: string, error_description?: string }

      if (!response.ok || !data.access_token) {
        const errorMsg = `Logo Rest Servisi Hatası - getToken: ${data.error || 'Erişim anahtarı alınamadı'}. ${data.error_description || ''} Durum: ${response.status} ${response.statusText}`
        consola.error(errorMsg, { responseData: data })
        throw new Error(errorMsg)
      }

      consola.success('Logo erişim anahtarı başarıyla alındı.')
      return data.access_token
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - getToken:', error)
      throw error // Re-throw after logging
    }
  },

  /**
   * Revokes an access token from the Logo REST API.
   */
  revokeToken: async ({ accessToken, veritabaniId }: { accessToken: string, veritabaniId: string }): Promise<void> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const restApiUrl = logoConfig.erp.rest_settings.rest_api_url

      if (!restApiUrl) {
        throw new Error(`Veritabanı ID için Logo REST API URL'si bulunamadı: ${veritabaniId}`)
      }

      consola.info(`Logo erişim anahtarı iptal ediliyor: ${restApiUrl}/revoke`)
      const response = await fetch(`${restApiUrl}/revoke`, { method: 'GET', headers: { Authorization: `Bearer ${accessToken}` } })

      if (!response.ok) {
        const data = await response.text()
        const errorMsg = `Logo Rest Servisi Hatası - revokeToken: Durum: ${response.status} ${response.statusText}. Yanıt: ${data}`
        consola.error(errorMsg)
        // Don't throw here, just log the error as revoke failure might not be critical
      }
      else {
        consola.success('Logo erişim anahtarı başarıyla iptal edildi.')
      }
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - revokeToken:', error)
      // Don't re-throw revoke errors
    }
  },

  /**
   * Posts a sales invoice to the Logo REST API.
   * Assumes the endpoint is /SalesInvoices.
   */
  postSatisFatura: async ({
    accessToken,
    invoiceData,
    veritabaniId,
  }: {
    accessToken: string
    invoiceData: SatisFaturaInput
    veritabaniId: string
  }): Promise<{ INTERNAL_REFERENCE: number }> => { // Assuming response structure
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const restApiUrl = logoConfig.erp.rest_settings.rest_api_url

      if (!restApiUrl) {
        throw new Error(`Veritabanı ID için Logo REST API URL'si bulunamadı: ${veritabaniId}`)
      }

      const endpoint = `${restApiUrl}/SalesInvoices` // Varsayılan endpoint
      consola.info(`Satış faturası Logo'ya gönderiliyor: ${endpoint}`)
      // Format the data according to the expected structure
      const formattedData = {
        ...invoiceData.INVOICE,
        DISPATCHES: invoiceData.DISPATCHES,
        TRANSACTIONS: invoiceData.TRANSACTIONS,
      }
      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(formattedData),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      // Try parsing JSON first, fallback to text if it fails
      let data: any
      try {
        data = await response.json()
      }
      catch {
        data = await response.text() // Get raw text if JSON parsing fails
      }

      if (!response.ok) {
        const errorDetail = typeof data === 'string' ? data : JSON.stringify(data)
        const errorMsg = `Logo Rest Servisi Hatası - postSatisFatura: Durum: ${response.status} ${response.statusText}. Yanıt: ${errorDetail}`
        consola.error(errorMsg, { requestBody: invoiceData })
        throw new Error(errorMsg)
      }

      // Assuming successful response contains INTERNAL_REFERENCE
      if (typeof data !== 'object' || data === null || typeof data.INTERNAL_REFERENCE !== 'number') {
        consola.warn('Logo Rest Servisi - postSatisFatura: Beklenmeyen yanıt formatı.', { responseData: data })
        // Attempt to provide a default or handle differently if necessary
        // For now, throw an error if INTERNAL_REFERENCE is missing/invalid
        throw new Error('Logo Rest Servisi - postSatisFatura: Yanıt formatı beklenmedik veya INTERNAL_REFERENCE eksik.')
      }

      consola.success(`Satış faturası Logo'ya başarıyla gönderildi. Referans No: ${data.INTERNAL_REFERENCE}`)
      return data as { INTERNAL_REFERENCE: number }
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - postSatisFatura:', error)
      throw error // Re-throw after logging
    }
  },
}

export default SatisFaturalariLogoRestService
