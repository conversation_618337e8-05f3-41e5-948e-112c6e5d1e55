import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import { consola } from 'consola'
import sql from 'mssql'
import DbService from '../../shared/services/db-service.ts'
import DatabaseService from './database-service.ts'
import LogoTrackingService from './logo-tracking-service.ts'
import LogoErpIntegration from './logo-erp-integration.ts'

/**
 * Service for processing direct SQL operations to Logo ERP
 * Used when use_rest=false
 */
const LogoSqlDirectProcessor = {

  processDirectSql: async ({
    invoice,
    veritabaniId,
    logoCredentials,
  }: {
    invoice: SatisFaturaInput
    veritabaniId: string
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ success: boolean, error?: string, logoRef?: number | undefined, ficheNo?: string, irsaliyeNo?: string }> => {
    let invoiceRef: number | undefined
    let ficheNo: string | undefined
    let stficheRef: number | undefined
    let stficheNo: string | undefined

    try {
      // Calculate totals from line items
      // Based on the example:
      // - TOTALDISCOUNTED: 223,64 (this is the net amount before VAT)
      // - TOTALVAT: 22,36
      // - GROSSTOTAL: 223,64 (this is the same as TOTALDISCOUNTED in the example)
      // - NETTOTAL: 246 (this is the total including VAT)

      let totalVat = 0
      let totalGross = 0
      let totalNet = 0
      let totalDiscounted = 0

      if (invoice.TRANSACTIONS?.items?.length) {
        for (const line of invoice.TRANSACTIONS.items) {
          const quantity = line.QUANTITY || 0
          const price = line.PRICE || 0
          const total = line.TOTAL || (quantity * price)
          const vatRate = line.VAT_RATE || 0
          const vatIncluded = line.VAT_INCLUDED || 0
          const discountRate = line.DISCOUNT_RATE || 0

          let lineVat = 0
          let lineNet = 0
          let lineDiscounted = 0
          let lineTotal = total

          // Apply discount if any
          if (discountRate > 0) {
            lineTotal = total * (1 - (discountRate / 100))
          }

          if (vatIncluded === 1) {
            // VAT included in price
            // Based on the example, for a total of 246 with VAT rate 10%:
            // lineDiscounted (VATMATRAH) = 223,64
            // lineVat = 22,36
            lineDiscounted = lineTotal / (1 + (vatRate / 100))
            lineVat = lineTotal - lineDiscounted
            lineNet = lineTotal // This is the total including VAT
          }
          else {
            // VAT not included
            lineDiscounted = lineTotal
            lineVat = lineTotal * (vatRate / 100)
            lineNet = lineTotal + lineVat
          }

          // Round to 2 decimal places to match the example
          lineVat = Math.round(lineVat * 100) / 100
          lineNet = Math.round(lineNet * 100) / 100
          lineDiscounted = Math.round(lineDiscounted * 100) / 100

          totalVat += lineVat
          totalNet += lineNet
          totalGross += lineDiscounted // GROSSTOTAL is the same as TOTALDISCOUNTED in the example
          totalDiscounted += lineDiscounted
        }
      }

      // Round totals to 2 decimal places to match the example
      totalVat = Math.round(totalVat * 100) / 100
      totalNet = Math.round(totalNet * 100) / 100
      totalGross = Math.round(totalGross * 100) / 100
      totalDiscounted = Math.round(totalDiscounted * 100) / 100

      // When use_rest=false, we need to calculate these fields ourselves
      // We don't update invoice.INVOICE directly because these fields don't exist in the SatisFaturaHeader interface
      // Instead, we'll pass the calculated totals to the insertLogoActualInvoice and insertLogoActualStfiche functions

      // 1. Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
      const invoiceResult = await LogoErpIntegration.insertLogoActualInvoice({
        invoice: invoice.INVOICE,
        veritabaniId: veritabaniId,
        requestData: invoice.requestData,
        totals: {
          totalVat,
          totalNet,
          totalGross,
          totalDiscounted,
        },
        logoCredentials: logoCredentials,
      })

      if (!invoiceResult.logicalref) {
        return { success: false, error: 'Logo INVOICE tablosuna ekleme başarısız oldu' }
      }
      invoiceRef = invoiceResult.logicalref
      ficheNo = invoiceResult.ficheno || ''

      // 2. Insert into actual Logo STFICHE table (LG_{FFF}_{DD}_STFICHE)
      const stficheResult = await LogoErpIntegration.insertLogoActualStfiche({
        invoice: invoice.INVOICE,
        invoiceRef: invoiceResult.logicalref!,
        ficheNo: invoiceResult.ficheno!,
        veritabaniId: veritabaniId,
        requestData: invoice.requestData,
        totals: {
          totalVat,
          totalNet,
          totalGross,
          totalDiscounted,
        },
        logoCredentials: logoCredentials,
      })

      if (!stficheResult.logicalref) {
        return { success: false, error: 'Logo STFICHE tablosuna ekleme başarısız oldu' }
      }
      stficheRef = stficheResult.logicalref
      stficheNo = stficheResult.ficheno

      // 3. Insert into actual Logo STLINE table (LG_{FFF}_{DD}_STLINE) for each transaction line
      if (invoice.TRANSACTIONS?.items?.length) {
        const stlinesSuccess = await LogoErpIntegration.insertLogoActualStlines({
          invoiceRef: invoiceResult.logicalref!,
          stficheRef: stficheResult.logicalref!,
          lines: invoice.TRANSACTIONS.items,
          veritabaniId: veritabaniId,
          invoiceDate: invoice.INVOICE.DATE,
        })

        if (!stlinesSuccess) {
          return { success: false, error: 'Logo STLINE tablosuna ekleme başarısız oldu' }
        }
      }

      // When use_rest=false, we don't need to insert into LogoSatisFaturalari, LogoSatisFaturalariIrsaliyesi, LogoSatisFaturalariSatirlari tables
      // because these are for logging logo-rest requests.
      // We need to insert into SatisFaturalari and SatisFaturalariSatirlari tables to track the invoice in our application
      // and also into our tracking tables (LogoInvoice, LogoStfiche, LogoStline) for consistency.

      // First, insert into SatisFaturalari table
      let satisFaturaId: number | undefined
      try {
        // Extract request data from invoice.requestData
        const requestData = invoice.requestData
        if (!requestData) {
          throw new Error('Request data is missing')
        }

        // Insert into SatisFaturalari table
        const satisFaturaResult = await DatabaseService.insertSatisFatura({
          requestData,
          logoFaturaNo: ficheNo,
          logoIrsaliyeNo: stficheNo,
          logoFaturaLogicalRef: invoiceRef,
          logoIrsaliyeLogicalRef: stficheRef,
          veritabaniId,
        })

        if (!satisFaturaResult.success) {
          consola.error('SatisFaturalari tablosuna ekleme başarısız oldu:', satisFaturaResult.error)
        }
        else {
          satisFaturaId = satisFaturaResult.id

          // Insert into SatisFaturalariSatirlari table
          if (invoice.TRANSACTIONS?.items?.length && satisFaturaId) {
            // Convert TRANSACTIONS items to SatisFaturaSatirRequest format
            const faturaSatirlari = invoice.TRANSACTIONS.items.map((item) => {
              return {
                satir_turu: item.TYPE,
                malzeme_kodu: item.MASTER_CODE,
                ambar_kodu: item.SOURCEINDEX,
                fabrika_kodu: item.FACTORY,
                hareket_ozel_kodu: item.AUXIL_CODE,
                miktar: item.QUANTITY,
                indirim_tutari: 0, // Not available in item
                birim_fiyat: item.PRICE,
                para_birimi: 'TL', // Default
                dovizli_birim_fiyat: item.EDT_PRICE,
                doviz_kuru: item.TC_XRATE,
                aciklama: item.DESCRIPTION,
                indirim_orani: item.DISCOUNT_RATE,
                birim_kodu: item.UNIT_CODE,
                kdv_orani: item.VAT_RATE,
                satis_elemani: item.SALEMANCODE,
                proje_kodu: item.PROJECT_CODE,
              }
            })

            const satirlarResult = await DatabaseService.insertSatisFaturaSatirlari({
              faturaSatirlari,
              satisFaturaId,
            })

            if (!satirlarResult.success) {
              consola.error('SatisFaturalariSatirlari tablosuna ekleme başarısız oldu:', satirlarResult.error)
            }
          }
        }
      }
      catch (error) {
        consola.warn('SatisFaturalari tablosuna ekleme başarısız oldu:', error)
      }

      // Insert into our tracking tables (LogoInvoice, LogoStfiche, LogoStline) for consistency
      try {
        // Insert into LogoInvoice tracking table
        await LogoTrackingService.insertLogoInvoice({
          invoice: {
            ...invoice.INVOICE,
            NUMBER: ficheNo,
          },
          veritabaniId,
          satisFaturaId,
        })
      }
      catch (error: any) {
        consola.warn('LogoInvoice tablosuna ekleme başarısız oldu (opsiyonel):', error.message || error)
      }

      try {
        // Insert into LogoStfiche tracking table
        await LogoTrackingService.insertLogoStfiche({
          invoice: {
            ...invoice.INVOICE,
            NUMBER: ficheNo,
          },
          invoiceRef,
          veritabaniId,
          satisFaturaId,
          irsaliyeNo: stficheNo,
        })
      }
      catch (error: any) {
        consola.warn('LogoStfiche tablosuna ekleme başarısız oldu (opsiyonel):', error.message || error)
      }

      if (invoice.TRANSACTIONS?.items?.length) {
        try {
          // Get satisFaturaSatirIds if satisFaturaId is available
          let satisFaturaSatirIds: number[] = []
          if (satisFaturaId) {
            try {
              const appConnection = await DbService.getConnection('db')
              const satirResult = await appConnection.request()
                .input('satis_fatura_id', sql.Int, satisFaturaId)
                .query(`
                  SELECT id FROM SatisFaturalariSatirlari
                  WHERE satis_fatura_id = @satis_fatura_id
                  ORDER BY id
                `)

              if (satirResult.recordset.length > 0) {
                satisFaturaSatirIds = satirResult.recordset.map((row: any) => row.id)
              }
            }
            catch (error) {
              consola.warn('SatisFaturalariSatirlari tablosundan ID alınamadı:', error)
            }
          }

          // Insert into LogoStline tracking table
          await LogoTrackingService.insertLogoStlines({
            invoiceRef,
            stficheRef,
            lines: invoice.TRANSACTIONS.items,
            veritabaniId,
            satisFaturaId,
            satisFaturaSatirIds,
          })
        }
        catch (error: any) {
          consola.warn('LogoStline tablosuna ekleme başarısız oldu (opsiyonel):', error.message || error)
        }
      }

      return {
        success: true,
        logoRef: invoiceRef,
        ficheNo,
        irsaliyeNo: stficheNo,
      }
    }
    catch (error: any) {
      consola.error('Doğrudan SQL fatura işlemi sırasında hata oluştu:', error)
      return { success: false, error: error.message, logoRef: invoiceRef }
    }
  },
}

export default LogoSqlDirectProcessor
