import type { z } from 'zod'
import type {
  SatinalmaFaturaHeader,
  SatinalmaFaturaInput,
  SatinalmaFaturaLineItem,
} from '../models/purchase-invoice.ts'
import type { satinalmaFaturalariSchema } from '../routes/satinalma-faturalari-routes.ts'
import { consola } from 'consola'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import SatinalmaFaturalariLogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Converts HH:MM:SS time string to Logo integer format.
 * Formula: HH * 16777216 + MM * 65536 + SS * 256
 * @param timeString Time in HH:MM:SS format
 * @returns Logo integer representation of time, or 0 if invalid.
 */
function convertTimeToLogoInt(timeString: string | undefined): number {
  if (!timeString || !timeFormatRegex.test(timeString)) {
    consola.warn(`Invalid or missing time format received: ${timeString}. Defaulting to 0.`)
    return 0
  }
  const parts = timeString.split(':')
  // Add checks for parts length and validity before parsing
  if (parts.length !== 3 || !parts[0] || !parts[1] || !parts[2]) { // Check parts are defined
    consola.warn(`Invalid time parts after split: ${timeString}. Defaulting to 0.`)
    return 0
  }
  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    consola.warn(`Failed to parse time parts as numbers: ${timeString}. Defaulting to 0.`)
    return 0
  }

  // Apply the formula
  return hours * 16777216 + minutes * 65536 + seconds * 256
}

/**
 * İstek verilerini Logo formatına dönüştürür
 * @param requestData İstek verileri
 * @returns Logo formatında veriler
 */
function convertRequestToLogoFormat(
  requestData: z.infer<typeof satinalmaFaturalariSchema>,
): SatinalmaFaturaInput {
  // Tarih ve saat formatını ayarla
  const invoiceDate = requestData.tarihi
  const invoiceTime = convertTimeToLogoInt(requestData.saati)

  // Belge tarihi
  const docDate = requestData.belge_tarihi || requestData.tarihi

  // Fatura başlık bilgilerini oluştur
  const invoice: SatinalmaFaturaHeader = {
    TYPE: requestData.fatura_turu,
    NUMBER: requestData.fatura_no || '~',
    DATE: invoiceDate,
    TIME: invoiceTime,
    DOC_NUMBER: requestData.belge_no,
    AUXIL_CODE: requestData.ozel_kod,
    ARP_CODE: requestData.cari_kodu,
    SOURCE_WH: requestData.ambar_kodu,
    FACTORY: requestData.fabrika_kodu,
    NOTES1: requestData.aciklama ? requestData.aciklama.substring(0, 300) : undefined,
    NOTES2: requestData.aciklama && requestData.aciklama.length > 300 ? requestData.aciklama.substring(300, 600) : undefined,
    NOTES3: requestData.aciklama && requestData.aciklama.length > 600 ? requestData.aciklama.substring(600, 900) : undefined,
    NOTES4: requestData.aciklama && requestData.aciklama.length > 900 ? requestData.aciklama.substring(900, 1200) : undefined,
    NOTES5: requestData.aciklama && requestData.aciklama.length > 1200 ? requestData.aciklama.substring(1200, 1500) : undefined,
    NOTES6: requestData.aciklama && requestData.aciklama.length > 1500 ? requestData.aciklama.substring(1500, 1800) : undefined,
    CURR_INVOICE: requestData.doviz_kuru ? 1 : undefined, // Döviz kullanılıyorsa 1
    TC_XRATE: requestData.doviz_kuru,
    // RC_XRATE will be set later after getting the reporting currency exchange rate
    DIVISION: requestData.isyeri_kodu,
    DEPARTMENT: requestData.bolum_kodu,
    PAYMENT_CODE: requestData.odeme_kodu,
    CURRSEL_TOTALS: 2, // TL
    CURRSEL_DETAILS: 2, // TL
    PROJECT_CODE: requestData.proje_kodu,
    AFFECT_RISK: 1, // Riski etkilesin
    DOC_DATE: docDate,
  }

  // Fatura satırlarını oluştur
  let transactions: { items: SatinalmaFaturaLineItem[] } | undefined

  if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
    const items: SatinalmaFaturaLineItem[] = requestData.fatura_satirlari.map((satir) => {
      return {
        TYPE: satir.satir_turu,
        MASTER_CODE: satir.malzeme_kodu,
        SOURCEINDEX: satir.ambar_kodu,
        FACTORY: satir.fabrika_kodu,
        AUXIL_CODE: satir.hareket_ozel_kodu,
        QUANTITY: satir.miktar,
        PRICE: satir.birim_fiyat,
        DESCRIPTION: satir.aciklama,
        UNIT_CODE: satir.birim_kodu,
        VAT_RATE: satir.kdv_orani,
        BILLED: 1, // Faturalandı
        EDT_PRICE: satir.dovizli_birim_fiyat,
        PROJECT_CODE: satir.proje_kodu,
        CANDEDUCT: satir.tevkifat_yapilabilir,
        DEDUCTION_PART1: satir.tevkifat_payi1,
        DEDUCTION_PART2: satir.tevkifat_payi2,
        DEDUCT_CODE: satir.tevkifat_kodu,
        DEDUCT_DEF: satir.tevkifat_aciklamasi,
        OHP_CODE1: satir.masraf_merkezi1,
        OHP_CODE3: satir.masraf_merkezi3,
        OHP_CODE4: satir.masraf_merkezi4,
      }
    })

    transactions = { items }
  }

  return {
    INVOICE: invoice,
    TRANSACTIONS: transactions,
    requestData,
  }
}

/**
 * Fatura yanıt tipi
 */
interface InvoiceResponse {
  status: string
  data?: any
  error?: string
  logoRef?: number
  ficheNo?: string
}

/**
 * REST API yanıt tipi
 */
interface RestApiResponse {
  INTERNAL_REFERENCE: number
}

/**
 * Fatura satırlarını işler ve maliyet grubu ve para birimi bilgilerini ayarlar
 */
async function processInvoiceLineItems(
  invoiceData: SatinalmaFaturaInput,
  requestPayload: z.infer<typeof satinalmaFaturalariSchema>,
  veritabaniId: string,
): Promise<void> {
  // Ana fatura için maliyet grubu ayarla
  if (invoiceData.INVOICE.SOURCE_WH) {
    const costGroup = await LogoSqlService.getSourceCostGrp({
      nr: invoiceData.INVOICE.SOURCE_WH,
      veritabaniId,
    })

    if (costGroup !== undefined) {
      invoiceData.INVOICE.SOURCE_COST_GRP = costGroup
    }
    else {
      consola.warn(`Ambar ${invoiceData.INVOICE.SOURCE_WH} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
      invoiceData.INVOICE.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP ?? 0
    }
  }

  // Satırlar için maliyet grubu ayarla
  if (invoiceData.TRANSACTIONS?.items) {
    for (const item of invoiceData.TRANSACTIONS.items) {
      if (item.SOURCEINDEX) {
        const costGroup = await LogoSqlService.getSourceCostGrp({
          nr: item.SOURCEINDEX,
          veritabaniId,
        })

        if (costGroup !== undefined) {
          item.SOURCE_COST_GRP = costGroup
        }
        else {
          consola.warn(`Ambar ${item.SOURCEINDEX} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
          item.SOURCE_COST_GRP = item.SOURCE_COST_GRP ?? 0
        }
      }
    }
  }

  // RC_XRATE (reporting currency exchange rate) hesapla
  try {
    // Firma raporlama para birimini al
    const firmRepCurr = await LogoLookupService.getFirmRepCurr(veritabaniId)
    if (firmRepCurr !== undefined) {
      // Fatura tarihine göre döviz kurunu al
      const exchangeRate = await LogoSqlService.getExchangeRateByType({
        date: requestPayload.tarihi,
        crtype: firmRepCurr,
        veritabaniId,
      })
      if (exchangeRate !== undefined) {
        invoiceData.INVOICE.RC_XRATE = exchangeRate
      }
      else {
        consola.warn(`Tarih: ${requestPayload.tarihi}, döviz: ${firmRepCurr} için döviz kuru bulunamadı, varsayılan olarak 0 kullanılıyor`)
        invoiceData.INVOICE.RC_XRATE = 0
      }
    }
  }
  catch (error) {
    consola.error('Raporlama para birimi kuru alınırken hata oluştu:', error)
    invoiceData.INVOICE.RC_XRATE = invoiceData.INVOICE.TC_XRATE // Fallback to TC_XRATE
  }
}

/**
 * Logo REST API ile fatura gönderimini gerçekleştirir
 */
async function sendInvoiceToRestApi(
  invoiceData: SatinalmaFaturaInput,
  veritabaniId: string,
  logoCredentials?: { kullanici_adi: string, sifre: string },
): Promise<{
    logoRestApiResponse?: RestApiResponse
    logoRestError?: string
    ficheNo?: string
  }> {
  let accessToken: string | null = null
  let logoRestApiResponse: RestApiResponse | undefined
  let logoRestError: string | undefined
  let ficheNo: string | undefined

  try {
    accessToken = await SatinalmaFaturalariLogoRestService.getToken({
      veritabaniId,
      logoCredentials,
    })
    logoRestApiResponse = await SatinalmaFaturalariLogoRestService.postSatinalmaFatura({
      accessToken,
      invoiceData,
      veritabaniId,
    })

    // LOGICALREF kullanarak FICHENO değerini al
    if (logoRestApiResponse?.INTERNAL_REFERENCE) {
      const invoiceInfo = await LogoSqlService.getInvoiceFichenoByLogicalref({
        logicalref: logoRestApiResponse.INTERNAL_REFERENCE,
        veritabaniId,
      })
      ficheNo = invoiceInfo?.ficheno
    }
  }
  catch (error) {
    logoRestError = error instanceof Error ? error.message : 'Bilinmeyen hata'
    consola.error('Logo REST API ile fatura gönderilirken hata oluştu:', error)
  }
  finally {
    // Token'ı iptal et
    if (accessToken) {
      try {
        await SatinalmaFaturalariLogoRestService.revokeToken({
          accessToken,
          veritabaniId,
        })
      }
      catch (error) {
        consola.error('Token iptal edilirken hata oluştu:', error)
      }
    }
  }

  return { logoRestApiResponse, logoRestError, ficheNo }
}

/**
 * Fatura verilerini yerel veritabanına kaydeder
 */
async function saveInvoiceToLocalDb(
  requestPayload: z.infer<typeof satinalmaFaturalariSchema>,
  invoiceData: SatinalmaFaturaInput,
  veritabaniId: string,
  logoRestError?: string,
  ficheNo?: string,
  logoRestApiResponse?: RestApiResponse,
): Promise<{
    success: boolean
    error?: string
    satinalmaFaturaDbId?: number
  }> {
  try {
    // Prepare JSON data for logging
    const invoiceJsonData = JSON.stringify(invoiceData.INVOICE)
    const stficheJsonData = logoRestApiResponse
      ? JSON.stringify({
          LOGICALREF: logoRestApiResponse.INTERNAL_REFERENCE,
          FICHENO: ficheNo,
          // Add other stfiche fields as needed
        })
      : undefined

    // Ana fatura bilgilerini kaydet
    const satinalmaFaturaResult = await LogoSqlService.insertSatinalmaFatura({
      requestData: requestPayload,
      errorMessage: logoRestError,
      logoFaturaNo: ficheNo,
      logoFaturaLogicalRef: logoRestApiResponse?.INTERNAL_REFERENCE,
      invoiceData: invoiceJsonData,
      stficheData: stficheJsonData,
      veritabaniId,
    })

    if (!satinalmaFaturaResult.success || !satinalmaFaturaResult.id) {
      return {
        success: false,
        error: satinalmaFaturaResult.error || 'Satınalma faturası kaydedilemedi',
      }
    }

    const satinalmaFaturaId = satinalmaFaturaResult.id

    // Fatura satırlarını kaydet
    if (requestPayload.fatura_satirlari && requestPayload.fatura_satirlari.length > 0) {
      // Prepare stline data for each line
      const stlineDataList = invoiceData.TRANSACTIONS?.items?.map(item => JSON.stringify(item)) || []

      const satirlarResult = await LogoSqlService.insertSatinalmaFaturaSatirlari({
        satinalmaFaturaId,
        satirlar: requestPayload.fatura_satirlari,
        stlineDataList,
      })

      if (!satirlarResult.success) {
        return {
          success: false,
          error: satirlarResult.error || 'Satınalma faturası satırları kaydedilemedi',
        }
      }
    }

    return {
      success: true,
      satinalmaFaturaDbId: satinalmaFaturaId,
    }
  }
  catch (error) {
    consola.error('Fatura verileri yerel veritabanına kaydedilirken hata oluştu:', error)
    return {
      success: false,
      error: `Veritabanı hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
    }
  }
}

/**
 * Fatura oluşturma işlemini yönetir
 */
async function handleCreateInvoice({
  invoiceData,
  veritabaniId,
  requestPayload,
}: {
  invoiceData: SatinalmaFaturaInput
  veritabaniId: string
  requestPayload: z.infer<typeof satinalmaFaturalariSchema>
}): Promise<InvoiceResponse> {
  // Girdi verilerini doğrula
  if (!invoiceData?.INVOICE || !veritabaniId || !requestPayload) {
    return { status: 'error', error: 'Geçersiz girdi verisi.' }
  }

  try {
    // Fatura verilerini Logo veritabanına göre doğrula
    const validationResult = await LogoSqlService.validateSatinalmaFaturaRequest({
      requestData: requestPayload,
      veritabaniId,
    })

    if (!validationResult.isValid) {
      return { status: 'error', error: validationResult.error || 'Fatura doğrulama hatası.' }
    }

    // Fatura satırlarını işle
    await processInvoiceLineItems(invoiceData, requestPayload, veritabaniId)

    // REST API kullanılıp kullanılmayacağını belirle
    const useRest = await LogoSqlService.getUseRestFlag(veritabaniId)

    let logoRestApiResponse: RestApiResponse | undefined
    let logoRestError: string | undefined
    let ficheNo: string | undefined

    // REST API kullanılıyorsa, faturayı REST API'ye gönder
    if (useRest) {
      const restResult = await sendInvoiceToRestApi(
        invoiceData,
        veritabaniId,
        requestPayload.logo,
      )
      logoRestApiResponse = restResult.logoRestApiResponse
      logoRestError = restResult.logoRestError
      ficheNo = restResult.ficheNo
    }

    // Fatura verilerini yerel veritabanına kaydet
    const saveResult = await saveInvoiceToLocalDb(
      requestPayload,
      invoiceData,
      veritabaniId,
      logoRestError,
      ficheNo,
      logoRestApiResponse,
    )

    if (!saveResult.success) {
      return {
        status: 'error',
        error: saveResult.error,
      }
    }

    // REST API kullanılıyorsa, REST API yanıtını döndür
    if (useRest) {
      return {
        status: 'success',
        data: {
          message: logoRestError
            ? `Logo REST API'ye gönderilirken hata oluştu, ancak fatura yerel veritabanına başarıyla kaydedildi.`
            : `Fatura Logo REST API'ye başarıyla gönderildi ve yerel veritabanına kaydedildi.`,
          id: saveResult.satinalmaFaturaDbId,
          useRest,
          ficheNo,
        },
        error: logoRestError,
        logoRef: logoRestApiResponse?.INTERNAL_REFERENCE,
        ficheNo,
      }
    }
    // REST API kullanılmıyorsa, doğrudan SQL ile işle
    else {
      const directSqlResult = await LogoSqlService.processDirectSql({
        invoice: invoiceData,
        veritabaniId,
        logoCredentials: requestPayload.logo,
      })

      if (!directSqlResult.success) {
        return {
          status: 'error',
          error: directSqlResult.error || 'Doğrudan SQL entegrasyonu işlenemedi.',
          logoRef: directSqlResult.logoRef,
        }
      }

      // Fatura numarasını al
      ficheNo = directSqlResult.ficheNo

      // Yerel veritabanına kaydet (logoRef ile birlikte)
      await saveInvoiceToLocalDb(
        requestPayload,
        invoiceData,
        veritabaniId,
        undefined, // logoRestError
        ficheNo,
        undefined, // logoRestApiResponse - skip REST API logging when use_rest=false
      )

      return {
        status: 'success',
        data: {
          message: 'Satınalma faturası doğrudan SQL ile başarıyla işlendi ve yerel veritabanına kaydedildi.',
          id: saveResult.satinalmaFaturaDbId,
          useRest,
          ficheNo,
        },
        logoRef: directSqlResult.logoRef,
        ficheNo,
      }
    }
  }
  catch (error) {
    consola.error('Fatura oluşturulurken beklenmeyen bir hata oluştu:', error)
    return {
      status: 'error',
      error: `İşlem hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
    }
  }
}

const SatinalmaFaturalariService = {
  convertRequestToLogoFormat,
  handleCreateInvoice,
  convertTimeToLogoInt,
}

export default SatinalmaFaturalariService
