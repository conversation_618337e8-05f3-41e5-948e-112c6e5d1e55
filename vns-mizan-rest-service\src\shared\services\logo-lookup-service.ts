import type { IResult } from 'mssql'
import consola from 'consola'
import sql from 'mssql'
import { getLogoConfigById } from '../utils/config-utils.ts'
import DbService from './db-service.ts'

interface CostGroupResult {
  COSTGRP: number
}

interface CurrencyTypeResult {
  CURTYPE: number
}

interface LogicalRefResult {
  LOGICALREF: number
}

interface ExchangeRateResult {
  RATES1: number
}

interface FirmRepCurrResult {
  FIRMREPCURR: number
}

interface ClientRefResult {
  LOGICALREF: number
}

interface UnitRefResult {
  LOGICALREF: number
}

interface FicheNoResult {
  FICHENO: string
}

interface FormatFicheNoResult {
  FICHENO: string
  PREFIX: string
  NUMBER_PART: string
}

const LogoLookupService = {
  /**
   * Fetches the COSTGRP from L_CAPIWHOUSE based on warehouse number.
   */
  getCostGroupFromWarehouse: async (
    warehouseNr: number | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (warehouseNr == null) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Use the new query format as specified
      const query = `
        SELECT COSTGRP
        FROM ${logoConfig.erp.logodb_master}..L_CAPIWHOUSE
        WHERE FIRMNR=${logoConfig.erp.firma_numarasi}
        AND NR=@warehouseNr
      `

      const result: IResult<CostGroupResult[]> = await logoConnection.request()
        .input('warehouseNr', sql.Int, warehouseNr)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].COSTGRP
      }
      consola.warn(`Ambar ${warehouseNr} için maliyet grubu bulunamadı, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Ambar ${warehouseNr} için maliyet grubu alınırken hata oluştu:`, error)
      return undefined
    }
  },

  /**
   * Fetches the CURTYPE from L_CURRENCYLIST based on currency code.
   */
  getCurrencyTypeFromCode: async (
    currencyCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!currencyCode) {
      return 0 // Default to 0 (TL) if no code provided?
    }
    const upperCurrencyCode = currencyCode.toUpperCase()

    if (upperCurrencyCode === 'TL' || upperCurrencyCode === 'TRY') {
      return 0
    }

    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically
      const tableName = `${logoConfig.erp.logodb_master || 'LG'}_CURRENCYLIST`

      const query = `
        SELECT TOP 1 CURTYPE
        FROM ${tableName}
        WHERE CURCODE = @currencyCode
        AND FIRMNR = ${logoConfig.erp.firma_numarasi}
      `
      // Removed FIRMNR from WHERE clause as L_CURRENCYLIST might be global or handled differently
      // Check Logo documentation if FIRMNR is needed here
      const result: IResult<CurrencyTypeResult[]> = await logoConnection.request()
        .input('currencyCode', sql.VarChar, upperCurrencyCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].CURTYPE
      }
      consola.warn(`Döviz tipi bulunamadı, döviz kodu: ${upperCurrencyCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Döviz tipi alınırken hata oluştu, döviz kodu: ${upperCurrencyCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_SLSMAN based on salesman code.
   */
  getSalesmanRefFromCode: async (
    salesmanCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!salesmanCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically
      const tableName = `${logoConfig.erp.logodb_master}..LG_SLSMAN`

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM ${tableName}
        WHERE CODE = @salesmanCode
        AND FIRMNR in (${logoConfig.erp.firma_numarasi},-1)
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('salesmanCode', sql.VarChar, salesmanCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Satış temsilcisi referansı bulunamadı, kod: ${salesmanCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Satış temsilcisi referansı alınırken hata oluştu, kod: ${salesmanCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_XXX_PROJECT based on project code.
   */
  getProjectRefFromCode: async (
    projectCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!projectCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      // Construct table name dynamically with firm number
      const tableName = `LG_${logoConfig.erp.firma_numarasi}_PROJECT`

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM ${tableName}
        WHERE CODE = @projectCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('projectCode', sql.VarChar, projectCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Proje referansı bulunamadı, kod: ${projectCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Proje referansı alınırken hata oluştu, kod: ${projectCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the FIRMREPCURR from L_CAPIFIRM based on firm number.
   */
  getFirmRepCurr: async (veritabaniId: string): Promise<number | undefined> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const tableName = `${logoConfig.erp.logodb_master}..L_CAPIFIRM`

      const query = `
        SELECT TOP 1 FIRMREPCURR
        FROM ${tableName}
        WHERE NR = @firmaNr
      `
      const result: IResult<FirmRepCurrResult[]> = await logoConnection.request()
        .input('firmaNr', sql.Int, logoConfig.erp.firma_numarasi)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].FIRMREPCURR
      }
      consola.warn(`Firma raporlama para birimi bulunamadı, firma no: ${logoConfig.erp.firma_numarasi}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Firma raporlama para birimi alınırken hata oluştu, veritabanı ID: ${veritabaniId}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the exchange rate (RATES1) from L_DAILYEXCHANGES based on date and currency type.
   */
  getExchangeRate: async (
    date: Date | string | undefined,
    currencyType: number | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!date || currencyType === undefined) {
      return undefined
    }

    // Convert string date to Date object if needed
    const dateObj = typeof date === 'string' ? new Date(date) : date

    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Determine which table to use for exchange rates
      const { recordset: tableExists } = await logoConnection.request().query(`
          SELECT OBJECT_ID('LG_EXCHANGE_${logoConfig.erp.firma_numarasi}') as table_exists
      `)

      const exchangeTableName = tableExists[0]?.table_exists
        ? `LG_EXCHANGE_${logoConfig.erp.firma_numarasi}`
        : `${logoConfig.erp.logodb_master}..L_DAILYEXCHANGES`

      const query = `
        SELECT TOP 1 RATES1
        FROM ${exchangeTableName}
        WHERE EDATE = @date
        AND CRTYPE = @currencyType
      `
      const result: IResult<ExchangeRateResult[]> = await logoConnection.request()
        .input('date', sql.Date, dateObj)
        .input('currencyType', sql.Int, currencyType)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].RATES1
      }
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Döviz kuru alınırken hata oluştu, tarih: ${dateObj?.toISOString()}, döviz tipi: ${currencyType}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_CLCARD based on client code.
   */
  getClientRefFromCode: async (
    clientCode: string | undefined,
    veritabaniId: string,
  ): Promise<{ logicalref: number } | undefined> => {
    if (!clientCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_CLCARD
        WHERE CODE = @clientCode
      `
      const result: IResult<ClientRefResult[]> = await logoConnection.request()
        .input('clientCode', sql.VarChar, clientCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return {
          logicalref: result.recordset[0].LOGICALREF,
        }
      }
      consola.warn(`Cari hesap referansı bulunamadı, kod: ${clientCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Cari hesap referansı alınırken hata oluştu, kod: ${clientCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_ITEMS based on item code.
   */
  getItemRefFromCode: async (
    itemCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!itemCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_ITEMS
        WHERE CODE = @itemCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('itemCode', sql.VarChar, itemCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Malzeme referansı bulunamadı, kod: ${itemCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Malzeme referansı alınırken hata oluştu, kod: ${itemCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_SRVCARD based on service code.
   */
  getServiceRefFromCode: async (
    serviceCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!serviceCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_SRVCARD
        WHERE CODE = @serviceCode
      `
      const result: IResult<LogicalRefResult[]> = await logoConnection.request()
        .input('serviceCode', sql.VarChar, serviceCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Hizmet referansı bulunamadı, kod: ${serviceCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Hizmet referansı alınırken hata oluştu, kod: ${serviceCode}:`, error)
      return undefined
    }
  },

  /**
   * Fetches the LOGICALREF from LG_{FFF}_UNITSETL based on unit code.
   */
  getUnitRefFromCode: async (
    unitCode: string | undefined,
    veritabaniId: string,
  ): Promise<number | undefined> => {
    if (!unitCode) {
      return undefined
    }
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      const query = `
        SELECT TOP 1 LOGICALREF
        FROM LG_${logoConfig.erp.firma_numarasi}_UNITSETL
        WHERE CODE = @unitCode
      `
      const result: IResult<UnitRefResult[]> = await logoConnection.request()
        .input('unitCode', sql.VarChar, unitCode)
        .query(query)

      if (result.recordset.length > 0 && result.recordset[0]) {
        return result.recordset[0].LOGICALREF
      }
      consola.warn(`Birim referansı bulunamadı, kod: ${unitCode}, veritabanı ID: ${veritabaniId}`)
      return undefined // Not found
    }
    catch (error) {
      consola.error(`Birim referansı alınırken hata oluştu, kod: ${unitCode}:`, error)
      return undefined
    }
  },

  /**
   * Generates a new FICHENO for the specified table and transaction code.
   */
  generateNewFicheNo: async ({
    trcode,
    tableName,
    veritabaniId,
  }: {
    trcode: number
    tableName: string
    veritabaniId: string
  }): Promise<string> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Get the last used number for this transaction type
      const query = `
        SELECT TOP 1 FICHENO
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_${tableName}
        WHERE TRCODE = @trcode
        ORDER BY LOGICALREF DESC
      `
      const result: IResult<FicheNoResult[]> = await logoConnection.request()
        .input('trcode', sql.SmallInt, trcode)
        .query(query)

      let newFicheNo = '00000001'

      if (result.recordset.length > 0 && result.recordset[0] && result.recordset[0].FICHENO) {
        const lastFicheNo = result.recordset[0].FICHENO
        // Extract the numeric part and increment
        const numericPart = lastFicheNo.replace(/\D/g, '')
        if (numericPart) {
          const nextNumber = Number.parseInt(numericPart, 10) + 1
          newFicheNo = nextNumber.toString().padStart(8, '0')
        }
      }

      return newFicheNo
    }
    catch (error) {
      consola.error(`Yeni fiş numarası oluşturulurken hata oluştu, tablo: ${tableName}, işlem kodu: ${trcode}:`, error)
      // Return a default value in case of error
      return `${new Date().getTime().toString().slice(-8)}`
    }
  },

  /**
   * Generates a new FICHENO based on the provided format.
   * Format uses "_" as a placeholder for numeric characters.
   * Also supports date placeholders:
   * - [gg] or [dd] for day (01-31)
   * - [aa] or [mm] for month (01-12)
   * - [yyyy] for 4-digit year (e.g., 2025)
   * - [yy] for 2-digit year (e.g., 25)
   *
   * Examples:
   * - "ABC____" will generate "ABC0001", "ABC0002", etc.
   * - "INV[yyyy]____" might generate "INV20250001"
   * - "DSP[mm][dd]___" might generate "DSP05150001"
   */
  generateFormattedFicheNo: async ({
    trcode,
    tableName,
    format,
    veritabaniId,
  }: {
    trcode: number
    tableName: string
    format: string
    veritabaniId: string
  }): Promise<string> => {
    try {
      if (!format) {
        // If no format is provided, use the default method
        return LogoLookupService.generateNewFicheNo({ trcode, tableName, veritabaniId })
      }

      // Process date placeholders
      const now = new Date()
      const day = now.getDate().toString().padStart(2, '0')
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const fullYear = now.getFullYear().toString()
      const shortYear = fullYear.substring(2)

      const processedFormat = format
        .replace(/\[gg\]/g, day)
        .replace(/\[dd\]/g, day)
        .replace(/\[aa\]/g, month)
        .replace(/\[mm\]/g, month)
        .replace(/\[yyyy\]/g, fullYear)
        .replace(/\[yy\]/g, shortYear)

      // If there are no underscores after processing date placeholders, or if the format doesn't end with an underscore,
      // throw an error because we can't generate a sequential number
      if (!processedFormat.includes('_') || !processedFormat.endsWith('_')) {
        throw new Error(`Format must end with at least one underscore character after processing date placeholders: ${processedFormat}`)
      }

      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      // Extract the prefix (everything before the first underscore)
      const prefixEndIndex = processedFormat.indexOf('_')
      const prefix = processedFormat.substring(0, prefixEndIndex)
      const numericLength = processedFormat.length - prefixEndIndex

      // Create a SQL LIKE pattern for the prefix
      const likePattern = `${prefix}%`

      // Get the last used number for this transaction type with the given prefix
      const query = `
        SELECT TOP 1 FICHENO,
               '${prefix}' AS PREFIX,
               SUBSTRING(FICHENO, LEN('${prefix}') + 1, LEN(FICHENO)) AS NUMBER_PART
        FROM LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_${tableName}
        WHERE TRCODE = @trcode
          AND FICHENO LIKE @likePattern
        ORDER BY LOGICALREF DESC
      `

      const result: IResult<FormatFicheNoResult[]> = await logoConnection.request()
        .input('trcode', sql.SmallInt, trcode)
        .input('likePattern', sql.VarChar, likePattern)
        .query(query)

      let nextNumber = 1

      if (result.recordset.length > 0 && result.recordset[0]) {
        const numberPart = result.recordset[0].NUMBER_PART
        // Try to parse the numeric part
        const parsedNumber = Number.parseInt(numberPart, 10)
        if (!Number.isNaN(parsedNumber)) {
          nextNumber = parsedNumber + 1
        }
      }

      // Format the new number with leading zeros
      const formattedNumber = nextNumber.toString().padStart(numericLength, '0')

      // Combine prefix and formatted number
      const newFicheNo = `${prefix}${formattedNumber}`

      return newFicheNo
    }
    catch (error) {
      consola.error(`Formatlanmış fiş numarası oluşturulurken hata oluştu, tablo: ${tableName}, işlem kodu: ${trcode}, format: ${format}:`, error)
      // Fall back to the default method in case of error
      return LogoLookupService.generateNewFicheNo({ trcode, tableName, veritabaniId })
    }
  },
}

export default LogoLookupService
