import type { z } from 'zod'
import type {
  SatisFaturaDispatch,
  SatisFaturaHeader,
  SatisFaturaInput,
  SatisFaturaLineItem,
  SatisFaturaSatirRequest,
} from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import SatisFaturalariLogoRestService from './logo-rest-service.ts'
import LogoSqlService from './logo-sql-service.ts'

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Converts HH:MM:SS time string to Logo integer format.
 * Formula: HH * 16777216 + MM * 65536 + SS * 256
 * @param timeString Time in HH:MM:SS format
 * @returns Logo integer representation of time, or 0 if invalid.
 */
function convertTimeToLogoInt(timeString: string | undefined): number {
  if (!timeString || !timeFormatRegex.test(timeString)) {
    consola.warn(`Invalid or missing time format received: ${timeString}. Defaulting to 0.`)
    return 0
  }
  const parts = timeString.split(':')
  // Add checks for parts length and validity before parsing
  if (parts.length !== 3 || !parts[0] || !parts[1] || !parts[2]) { // Check parts are defined
    consola.warn(`Invalid time parts after split: ${timeString}. Defaulting to 0.`)
    return 0
  }
  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)

  if (Number.isNaN(hours) || Number.isNaN(minutes) || Number.isNaN(seconds)) {
    consola.warn(`Failed to parse time parts as numbers: ${timeString}. Defaulting to 0.`)
    return 0
  }

  // Apply the formula
  return hours * 16777216 + minutes * 65536 + seconds * 256
}

/**
 * Request verisini LOGO API formatına dönüştürür
 */
async function transformToLogoFormat(requestData: z.infer<typeof satisFaturalariSchema>): Promise<SatisFaturaInput> {
  // INVOICE bölümünü oluştur
  const invoice: SatisFaturaHeader = {
    TYPE: requestData.fatura_turu,
    NUMBER: requestData.fatura_no || '~',
    DATE: requestData.tarihi,
    TIME: convertTimeToLogoInt(requestData.saati), // Pass string directly
    DOC_NUMBER: requestData.belge_no,
    AUXIL_CODE: requestData.ozel_kod,
    ARP_CODE: requestData.cari_kodu,
    SOURCE_WH: requestData.ambar_kodu,
    FACTORY: requestData.fabrika_kodu,
    // SOURCE_COST_GRP ambar_kodu'na göre veritabanından alınacak
    DIVISION: requestData.isyeri_kodu || 0,
    DEPARTMENT: requestData.bolum_kodu || 0,
    SALESMAN_CODE: requestData.satis_elemani,
    PROJECT_CODE: requestData.proje_kodu,
    DOC_DATE: requestData.belge_tarihi || requestData.tarihi,
    AFFECT_RISK: 1, // Sabit değer
    PROFILE_ID: 2, // Sabit değer
    EINSTEAD_OF_DISPATCH: 1, // Sabit değer
    DataObjectParameter: {
      FillAccCodesOnPreSave: true, // Sabit değer
    },
  }

  // Açıklamayı 6 parçaya böl
  if (requestData.aciklama) {
    const maxPartLength = 300
    for (let i = 0; i < 6; i++) {
      const start = i * maxPartLength
      const end = start + maxPartLength
      const part = requestData.aciklama.substring(start, end)

      if (part) {
        const fieldName = `NOTES${i + 1}` as keyof SatisFaturaHeader
        // @ts-expect-error - This is a valid assignment
        invoice[fieldName] = part
      }
    }
  }

  // TRANSACTIONS bölümünü oluştur
  let transactions: { items: SatisFaturaLineItem[] } | undefined

  if (requestData.fatura_satirlari && requestData.fatura_satirlari.length > 0) {
    const items: SatisFaturaLineItem[] = []

    // Para birimi kontrolleri için TL/TRY dışında, aynı para birimine sahip satırları tespit et
    const nonTLCurrencies = new Set<string>()
    let allSameCurrency = true
    let lastCurrency = ''

    if (requestData.fatura_satirlari && Array.isArray(requestData.fatura_satirlari) && requestData.fatura_satirlari.length > 0) {
      for (const satir of requestData.fatura_satirlari) {
        if (satir && satir.para_birimi && satir.para_birimi.toUpperCase() !== 'TL' && satir.para_birimi.toUpperCase() !== 'TRY') {
          nonTLCurrencies.add(satir.para_birimi.toUpperCase())

          if (lastCurrency === '') {
            lastCurrency = satir.para_birimi.toUpperCase()
          }
          else if (lastCurrency !== satir.para_birimi.toUpperCase()) {
            allSameCurrency = false
          }
        }
      }
    }

    // CURR_INVOICE, CURRSEL_TOTALS, CURRSEL_DETAILS değerlerini ayarla
    if (nonTLCurrencies.size === 0) {
      // Tüm satırlar TL/TRY
      invoice.CURR_INVOICE = 0
      invoice.CURRSEL_TOTALS = 1
      invoice.CURRSEL_DETAILS = 0
    }
    else if (nonTLCurrencies.size === 1 && allSameCurrency) {
      // Tüm satırlar aynı döviz - CURR_INVOICE döviz koduna göre LOGO SQL servisinden alınacak
      // Döviz kodunu al
      const currencyCode = lastCurrency
      try {
        // Asenkron işlemi bekle
        const currType = await LogoLookupService.getCurrencyTypeFromCode(currencyCode, requestData.veritabani_id)
        if (currType !== undefined) {
          invoice.CURR_INVOICE = currType
        }
        else {
          // Eğer döviz kodu bulunamazsa varsayılan olarak 1 (USD) kullan
          consola.warn(`${currencyCode} için döviz tipi bulunamadı, varsayılan olarak 1 (USD) kullanılıyor`)
          invoice.CURR_INVOICE = 1
        }
      }
      catch (error) {
        consola.error(`${currencyCode} için döviz tipi alınırken hata oluştu:`, error)
        invoice.CURR_INVOICE = 1 // Hata durumunda varsayılan olarak 1 (USD) kullan
      }

      invoice.CURRSEL_TOTALS = 2
      invoice.CURRSEL_DETAILS = 2

      // Döviz kuru kontrolü
      const firstSatir = requestData.fatura_satirlari?.[0]
      if (firstSatir) {
        const allSameCurrencyRate = requestData.fatura_satirlari!.every(
          s => s.doviz_kuru === firstSatir.doviz_kuru,
        )
        if (allSameCurrencyRate && firstSatir.doviz_kuru) {
          invoice.TC_XRATE = firstSatir.doviz_kuru
        }
        else if (requestData.doviz_kuru) {
          invoice.TC_XRATE = requestData.doviz_kuru
        }
      }
      else if (requestData.doviz_kuru) {
        invoice.TC_XRATE = requestData.doviz_kuru
      }
    }
    else {
      // Farklı dövizler
      invoice.CURR_INVOICE = 0
      invoice.CURRSEL_TOTALS = 2
      invoice.CURRSEL_DETAILS = 4
    }

    // RC_XRATE (reporting currency exchange rate) hesapla
    try {
      // Firma raporlama para birimini al
      const firmRepCurr = await LogoLookupService.getFirmRepCurr(requestData.veritabani_id)
      if (firmRepCurr !== undefined) {
        // Fatura tarihine göre döviz kurunu al
        const exchangeRate = await LogoSqlService.getExchangeRateByType({
          date: requestData.tarihi,
          crtype: firmRepCurr,
          veritabaniId: requestData.veritabani_id,
        })
        if (exchangeRate !== undefined) {
          invoice.RC_XRATE = exchangeRate
        }
        else {
          consola.warn(`Tarih: ${requestData.tarihi}, döviz: ${firmRepCurr} için döviz kuru bulunamadı, varsayılan olarak 0 kullanılıyor`)
          invoice.RC_XRATE = 0
        }
      }
      else {
        consola.warn(`Firma raporlama para birimi bulunamadı, RC_XRATE varsayılan olarak 0 kullanılıyor`)
        invoice.RC_XRATE = 0
      }
    }
    catch (error) {
      consola.error(`RC_XRATE alınırken hata oluştu:`, error)
      invoice.RC_XRATE = 0
    }

    // Satırları dönüştür
    for (const satir of requestData.fatura_satirlari) {
      const item: SatisFaturaLineItem = {
        TYPE: satir.satir_turu,
        MASTER_CODE: satir.malzeme_kodu || '',
        DISCEXP_CALC: 0, // Varsayılan değer
        SOURCEINDEX: satir.ambar_kodu || invoice.SOURCE_WH || 0,
        SOURCE_COST_GRP: invoice.SOURCE_COST_GRP, // Map to SOURCE_COST_GRP, ensure invoice.SOURCE_COST_GRP is set earlier
        FACTORY: satir.fabrika_kodu || invoice.FACTORY || 0,
        AUXIL_CODE: satir.hareket_ozel_kodu,
        DESCRIPTION: satir.aciklama || '',
        SALEMANCODE: satir.satis_elemani || invoice.SALESMAN_CODE,
        PROJECT_CODE: satir.proje_kodu || invoice.PROJECT_CODE,
        AFFECT_RISK: 1, // Sabit değer
        BILLED: 1, // Sabit değer
      }

      // Satır türüne göre farklı alanları ayarla
      if (satir.satir_turu === 0) { // Malzeme satırı
        item.QUANTITY = satir.miktar || 0
        item.TOTAL = 0 // Bu değer kullanılmıyor, hesaplanıyor
        item.PRICE = satir.birim_fiyat || 0

        // Birim kodu ayarla - eğer belirtilmemişse malzeme kartından al
        if (satir.birim_kodu) {
          item.UNIT_CODE = satir.birim_kodu
        }
        else if (satir.malzeme_kodu) {
          try {
            const unitCode = await LogoSqlService.getUnitCodeForItem({
              code: satir.malzeme_kodu,
              veritabaniId: requestData.veritabani_id,
            })
            item.UNIT_CODE = unitCode
          }
          catch {
            consola.warn(`Malzeme ${satir.malzeme_kodu} için birim kodu alınamadı, varsayılan olarak ADET kullanılıyor`)
            item.UNIT_CODE = 'ADET'
          }
        }
        else {
          item.UNIT_CODE = 'ADET'
        }

        item.VAT_RATE = satir.kdv_orani || 0
        item.DISCOUNT_RATE = satir.indirim_orani || 0
        // KDV dahil durumu: Perakende satışta 1, toptan satışta 0
        item.VAT_INCLUDED = requestData.fatura_turu === 7 ? 1 : 0

        // Para birimi ayarları
        if (satir.para_birimi && satir.para_birimi.toUpperCase() !== 'TL' && satir.para_birimi.toUpperCase() !== 'TRY') {
          // EDT_CURR para birimine göre LOGO SQL servisinden al
          try {
            const currType = await LogoLookupService.getCurrencyTypeFromCode(satir.para_birimi, requestData.veritabani_id)
            if (currType !== undefined) {
              item.EDT_CURR = currType
            }
            else {
              // Eğer döviz kodu bulunamazsa varsayılan olarak 1 (USD) kullan
              consola.warn(`${satir.para_birimi} için döviz tipi bulunamadı, varsayılan olarak 1 (USD) kullanılıyor`)
              item.EDT_CURR = 1
            }
          }
          catch (error) {
            consola.error(`${satir.para_birimi} için döviz tipi alınırken hata oluştu:`, error)
            item.EDT_CURR = 1 // Hata durumunda varsayılan olarak 1 (USD) kullan
          }
          item.EDT_PRICE = satir.dovizli_birim_fiyat || 0
          item.TC_XRATE = satir.doviz_kuru || 0

          // Kontroller
          if (satir.birim_fiyat && satir.dovizli_birim_fiyat && satir.doviz_kuru) {
            // Birim fiyat kontrol et
            const calculatedPrice = satir.dovizli_birim_fiyat * satir.doviz_kuru
            if (Math.abs(calculatedPrice - satir.birim_fiyat) > 0.01) {
              throw new Error('Dövizli birim fiyat ile TL birim fiyat hesaplaması uyuşmuyor.')
            }
          }
        }
        else {
          item.EDT_CURR = 0
          item.EDT_PRICE = 0
          item.TC_XRATE = 0
        }
      }
      else if (satir.satir_turu === 2) { // İndirim satırı
        item.QUANTITY = 0
        item.PRICE = 0
        item.UNIT_CODE = ''
        item.VAT_RATE = satir.kdv_orani || 0
        item.VAT_INCLUDED = requestData.fatura_turu === 7 ? 1 : 0
        item.EDT_CURR = 0
        item.EDT_PRICE = 0
        item.TC_XRATE = 0

        // İndirim oranı veya tutarı ayarla
        if (satir.indirim_orani != null) {
          item.DISCOUNT_RATE = satir.indirim_orani
          item.DISCEXP_CALC = 0
          item.TOTAL = 0
        }
        else if (satir.indirim_tutari != null) {
          item.DISCOUNT_RATE = 0
          item.DISCEXP_CALC = 1
          item.TOTAL = satir.indirim_tutari
        }
      }

      items.push(item)
    }

    transactions = { items }
  }

  // DISPATCHES bölümünü oluştur
  let dispatches: { items: SatisFaturaDispatch[] } | undefined

  if (requestData.irsaliye_no || requestData.irsaliye_tarihi || requestData.irsaliye_saati) {
    const dispatchDate = requestData.irsaliye_tarihi || requestData.tarihi
    const dispatchTimeInput = requestData.irsaliye_saati || requestData.saati // Could still be undefined if both missing
    const dispatchTime = convertTimeToLogoInt(dispatchTimeInput)

    // If time is 0 (due to missing/invalid input), maybe throw error or default differently?
    // For now, it defaults to 0 as per convertTimeToLogoInt logic.
    // If a dispatch is intended, time might be mandatory.
    // Let's assume time 0 is acceptable for now if not provided.

    const dispatch: SatisFaturaDispatch = {
      TYPE: requestData.fatura_turu,
      NUMBER: requestData.irsaliye_no || '~',
      DATE: dispatchDate,
      TIME: dispatchTime,
    }
    dispatches = { items: [dispatch] }
  }

  return {
    INVOICE: invoice,
    TRANSACTIONS: transactions,
    DISPATCHES: dispatches,
    requestData,
  }
}

/**
 * Fatura yanıt tipi
 */
interface InvoiceResponse {
  status: string
  data?: any
  error?: string
  logoRef?: number
  ficheNo?: string
  irsaliyeNo?: string
}

/**
 * REST API yanıt tipi
 */
interface RestApiResponse {
  INTERNAL_REFERENCE: number
  DISPATCHES?: {
    items: {
      INTERNAL_REFERENCE?: number
      NUMBER?: string
      DATE?: string
      TIME?: number
    }[]
  }
}

/**
 * Fatura satırlarını işler ve maliyet grubu ve para birimi bilgilerini ayarlar
 */
async function processInvoiceLineItems(
  invoiceData: SatisFaturaInput,
  requestPayload: z.infer<typeof satisFaturalariSchema>,
  veritabaniId: string,
): Promise<void> {
  // Ana fatura için maliyet grubu ayarla
  if (invoiceData.INVOICE.SOURCE_WH) {
    const costGroup = await LogoSqlService.getSourceCostGrp({
      nr: invoiceData.INVOICE.SOURCE_WH,
      veritabaniId,
    })

    if (costGroup !== undefined) {
      invoiceData.INVOICE.SOURCE_COST_GRP = costGroup
    }
    else {
      consola.warn(`Ambar ${invoiceData.INVOICE.SOURCE_WH} için maliyet grubu bulunamadı. Varsayılan değer kullanılıyor.`)
      invoiceData.INVOICE.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP ?? 0
    }
  }

  // Fatura satırlarını işle
  if (invoiceData.TRANSACTIONS?.items && requestPayload.fatura_satirlari) {
    if (invoiceData.TRANSACTIONS.items.length === requestPayload.fatura_satirlari.length) {
      await Promise.all(invoiceData.TRANSACTIONS.items.map(async (lineItem, i) => {
        const requestLine = requestPayload.fatura_satirlari?.[i]

        // Her satır için maliyet grubu ayarla
        if (requestLine?.ambar_kodu) {
          const lineCostGroup = await LogoSqlService.getSourceCostGrp({
            nr: requestLine.ambar_kodu,
            veritabaniId,
          })

          if (lineCostGroup !== undefined) {
            lineItem.SOURCE_COST_GRP = lineCostGroup
          }
          else {
            // Satır için maliyet grubu bulunamazsa, ana faturanın maliyet grubunu kullan
            lineItem.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP
          }
        }
        else {
          // Satır için ambar kodu belirtilmemişse, ana faturanın maliyet grubunu kullan
          lineItem.SOURCE_COST_GRP = invoiceData.INVOICE.SOURCE_COST_GRP
        }

        // Para birimi tipini işle
        if (requestLine?.para_birimi && requestLine.para_birimi.toUpperCase() !== 'TL' && requestLine.para_birimi.toUpperCase() !== 'TRY') {
          const currencyType = await LogoLookupService.getCurrencyTypeFromCode(
            requestLine.para_birimi,
            veritabaniId,
          )

          if (currencyType !== undefined) {
            lineItem.EDT_CURR = currencyType
          }
          else {
            consola.warn(`"${requestLine.para_birimi}" para birimi için döviz tipi bulunamadı. EDT_CURR değeri 0 olarak ayarlandı.`)
            lineItem.EDT_CURR = 0
          }
        }
        else {
          lineItem.EDT_CURR = 0
        }
      }))
    }
    else {
      consola.error('Dönüştürülen işlem kalemleri ile istek satırları arasında uyumsuzluk tespit edildi.')
      throw new Error('Fatura satırları işlenirken tutarsızlık tespit edildi (lookup).')
    }
  }
}

/**
 * Logo REST API ile fatura gönderimini gerçekleştirir
 */
async function sendInvoiceToRestApi(
  invoiceData: SatisFaturaInput,
  veritabaniId: string,
  logoCredentials?: { kullanici_adi: string, sifre: string },
): Promise<{
    logoRestApiResponse?: RestApiResponse
    logoRestError?: string
    ficheNo?: string
    irsaliyeNo?: string
  }> {
  let accessToken: string | null = null
  let logoRestApiResponse: RestApiResponse | undefined
  let logoRestError: string | undefined
  let ficheNo: string | undefined
  let irsaliyeNo: string | undefined

  try {
    accessToken = await SatisFaturalariLogoRestService.getToken({
      veritabaniId,
      logoCredentials,
    })
    logoRestApiResponse = await SatisFaturalariLogoRestService.postSatisFatura({
      accessToken,
      invoiceData,
      veritabaniId,
    })

    // LOGICALREF kullanarak FICHENO değerini al
    if (logoRestApiResponse?.INTERNAL_REFERENCE) {
      const invoiceInfo = await LogoSqlService.getInvoiceFichenoByLogicalref({
        logicalref: logoRestApiResponse.INTERNAL_REFERENCE,
        veritabaniId,
      })
      ficheNo = invoiceInfo?.ficheno

      // İrsaliye numarasını doğrudan REST API yanıtından al (eğer varsa)
      if (logoRestApiResponse.DISPATCHES?.items && logoRestApiResponse.DISPATCHES.items.length > 0) {
        const dispatchItem = logoRestApiResponse.DISPATCHES.items[0]
        if (dispatchItem && dispatchItem.NUMBER) {
          // NUMBER doğrudan irsaliye numarasıdır (FICHENO)
          irsaliyeNo = dispatchItem.NUMBER

          // Eğer gerekirse, NUMBER kullanarak LOGICALREF'i alabiliriz
          if (!dispatchItem.INTERNAL_REFERENCE && irsaliyeNo) {
            // Try to get LOGICALREF from FICHENO, silently handle any errors
            const irsaliyeLogicalRef = await LogoSqlService.getLogicalRefFromStficheNo(irsaliyeNo, veritabaniId).catch(() => undefined)
            if (irsaliyeLogicalRef) {
              // Burada logoRestApiResponse'a INTERNAL_REFERENCE ekleyebiliriz
              dispatchItem.INTERNAL_REFERENCE = irsaliyeLogicalRef
            }
          }
        }
      }
    }
  }
  catch (restError: any) {
    consola.error('Logo REST API\'ye fatura gönderilirken hata oluştu:', restError)
    logoRestError = restError.message || 'Logo REST API hatası'
  }
  finally {
    if (accessToken) {
      await SatisFaturalariLogoRestService.revokeToken({ accessToken, veritabaniId })
    }
  }

  return { logoRestApiResponse, logoRestError, ficheNo, irsaliyeNo }
}

/**
 * Fatura verilerini yerel veritabanına kaydeder
 */
async function saveInvoiceToLocalDb(
  requestPayload: z.infer<typeof satisFaturalariSchema>,
  invoiceData: SatisFaturaInput,
  veritabaniId: string,
  logoRestError?: string,
  ficheNo?: string,
  irsaliyeNo?: string,
  logoRestApiResponse?: RestApiResponse,
): Promise<{
    success: boolean
    error?: string
    satisFaturaDbId?: number
    logoSatisFaturaId?: number
  }> {
  try {
    // Ana fatura bilgilerini kaydet
    const satisFaturaResult = await LogoSqlService.insertSatisFatura({
      requestData: requestPayload,
      errorMessage: logoRestError,
      logoFaturaNo: ficheNo,
      logoIrsaliyeNo: irsaliyeNo,
      logoFaturaLogicalRef: logoRestApiResponse?.INTERNAL_REFERENCE,
      logoIrsaliyeLogicalRef: logoRestApiResponse?.DISPATCHES?.items?.[0]?.INTERNAL_REFERENCE,
      logoKullaniciAdi: requestPayload.logo?.kullanici_adi,
    })

    if (!satisFaturaResult.success || !satisFaturaResult.id) {
      return {
        success: false,
        error: satisFaturaResult.error || 'Fatura ana bilgileri kaydedilemedi.',
      }
    }

    const satisFaturaDbId = satisFaturaResult.id

    // Fatura satırlarını kaydet
    if (requestPayload.fatura_satirlari?.length) {
      const satirlarResult = await LogoSqlService.insertSatisFaturaSatirlari({
        faturaSatirlari: requestPayload.fatura_satirlari as SatisFaturaSatirRequest[],
        satisFaturaId: satisFaturaDbId,
      })

      if (!satirlarResult.success) {
        return {
          success: false,
          error: satirlarResult.error || 'Fatura satırları kaydedilemedi.',
          satisFaturaDbId,
        }
      }
    }

    // REST API kullanılıyorsa Logo verilerini de kaydet
    if (logoRestApiResponse) {
      // Logo fatura bilgilerini kaydet
      const logoFaturaResult = await LogoSqlService.insertLogoSatisFatura({
        invoice: invoiceData.INVOICE,
        veritabaniId,
        errorMessage: logoRestError,
        logoFaturaNo: ficheNo,
        logoFaturaLogicalRef: logoRestApiResponse.INTERNAL_REFERENCE,
        logoKullaniciAdi: requestPayload.logo?.kullanici_adi,
      })

      if (!logoFaturaResult.success || !logoFaturaResult.id) {
        return {
          success: false,
          error: logoFaturaResult.error || 'Lokal Logo fatura başlık kaydı oluşturulamadı.',
          satisFaturaDbId,
        }
      }

      const logoSatisFaturaId = logoFaturaResult.id

      // Logo irsaliye bilgilerini kaydet
      if (invoiceData.DISPATCHES?.items?.length) {
        const logoIrsaliyelerResult = await LogoSqlService.insertLogoSatisFaturaIrsaliyesi({
          irsaliyeler: invoiceData.DISPATCHES.items,
          logoSatisFaturaId,
          veritabaniId,
          errorMessage: logoRestError,
          logoIrsaliyeNo: irsaliyeNo,
          logoIrsaliyeLogicalRef: logoRestApiResponse.DISPATCHES?.items?.[0]?.INTERNAL_REFERENCE,
        })

        if (!logoIrsaliyelerResult.success) {
          return {
            success: false,
            error: logoIrsaliyelerResult.error || 'Lokal Logo fatura irsaliye kaydı oluşturulamadı.',
            satisFaturaDbId,
            logoSatisFaturaId,
          }
        }
      }

      // Logo fatura satırlarını kaydet
      if (invoiceData.TRANSACTIONS?.items?.length) {
        const logoSatirlarResult = await LogoSqlService.insertLogoSatisFaturaSatirlari({
          satirlar: invoiceData.TRANSACTIONS.items,
          logoSatisFaturaId,
          errorMessage: logoRestError,
        })

        if (!logoSatirlarResult.success) {
          return {
            success: false,
            error: logoSatirlarResult.error || 'Lokal Logo fatura satır kaydı oluşturulamadı.',
            satisFaturaDbId,
            logoSatisFaturaId,
          }
        }
      }

      return {
        success: true,
        satisFaturaDbId,
        logoSatisFaturaId,
      }
    }

    return {
      success: true,
      satisFaturaDbId,
    }
  }
  catch (error: any) {
    consola.error('Fatura verileri kaydedilirken hata oluştu:', error)
    return {
      success: false,
      error: error.message || 'Fatura verileri kaydedilirken beklenmeyen bir hata oluştu.',
    }
  }
}

/**
 * Fatura oluşturma işlemini yönetir
 */
async function handleCreateInvoice({
  invoiceData,
  veritabaniId,
  requestPayload,
}: {
  invoiceData: SatisFaturaInput
  veritabaniId: string
  requestPayload: z.infer<typeof satisFaturalariSchema>
}): Promise<InvoiceResponse> {
  // Girdi verilerini doğrula
  if (!invoiceData?.INVOICE || !veritabaniId || !requestPayload) {
    return { status: 'error', error: 'Geçersiz girdi verisi.' }
  }

  try {
    // Fatura verilerini Logo veritabanına göre doğrula
    const validationResult = await LogoSqlService.validateSatisFaturaRequest({
      requestData: requestPayload,
      veritabaniId,
    })

    if (!validationResult.isValid) {
      return { status: 'error', error: validationResult.error || 'Fatura doğrulama hatası.' }
    }

    // Fatura satırlarını işle
    await processInvoiceLineItems(invoiceData, requestPayload, veritabaniId)

    // REST API kullanılıp kullanılmayacağını belirle
    const useRest = await LogoSqlService.getUseRestFlag(veritabaniId)

    let logoRestApiResponse: RestApiResponse | undefined
    let logoRestError: string | undefined
    let ficheNo: string | undefined
    let irsaliyeNo: string | undefined

    // REST API kullanılıyorsa, faturayı REST API'ye gönder
    if (useRest) {
      const restResult = await sendInvoiceToRestApi(
        invoiceData,
        veritabaniId,
        requestPayload.logo,
      )
      logoRestApiResponse = restResult.logoRestApiResponse
      logoRestError = restResult.logoRestError
      ficheNo = restResult.ficheNo
      irsaliyeNo = restResult.irsaliyeNo
    }

    // Fatura verilerini yerel veritabanına kaydet
    const saveResult = await saveInvoiceToLocalDb(
      requestPayload,
      invoiceData,
      veritabaniId,
      logoRestError,
      ficheNo,
      irsaliyeNo,
      logoRestApiResponse,
    )

    if (!saveResult.success) {
      return {
        status: 'error',
        error: saveResult.error,
      }
    }

    // REST API kullanılıyorsa, REST API yanıtını döndür
    if (useRest) {
      return {
        status: 'success',
        data: {
          message: logoRestError
            ? `Logo REST API'ye gönderilirken hata oluştu, ancak fatura yerel veritabanına başarıyla kaydedildi.`
            : `Fatura Logo REST API'ye başarıyla gönderildi ve yerel veritabanına kaydedildi.`,
          id: saveResult.satisFaturaDbId,
          useRest,
          ficheNo,
          irsaliyeNo,
        },
        error: logoRestError,
        logoRef: logoRestApiResponse?.INTERNAL_REFERENCE,
        ficheNo,
        irsaliyeNo,
      }
    }
    // REST API kullanılmıyorsa, doğrudan SQL ile işle
    else {
      const directSqlResult = await LogoSqlService.processDirectSql({
        invoice: invoiceData,
        veritabaniId,
        logoCredentials: requestPayload.logo,
      })

      if (!directSqlResult.success) {
        return {
          status: 'error',
          error: directSqlResult.error || 'Doğrudan SQL entegrasyonu işlenemedi.',
          logoRef: directSqlResult.logoRef,
        }
      }

      // Fatura ve irsaliye numaralarını al
      ficheNo = directSqlResult.ficheNo
      irsaliyeNo = directSqlResult.irsaliyeNo

      // Yerel veritabanına kaydet (logoRef ile birlikte)
      // When use_rest=false, we don't need to insert into LogoSatisFaturalari, LogoSatisFaturalariIrsaliyesi, LogoSatisFaturalariSatirlari tables
      // because these are for logging logo-rest requests.
      await saveInvoiceToLocalDb(
        requestPayload,
        invoiceData,
        veritabaniId,
        undefined, // logoRestError
        ficheNo,
        irsaliyeNo,
        undefined, // Skip REST API logging tables when use_rest=false
      )

      return {
        status: 'success',
        data: {
          message: 'Fatura doğrudan SQL ile başarıyla işlendi ve yerel veritabanına kaydedildi.',
          id: saveResult.satisFaturaDbId,
          useRest,
          ficheNo,
          irsaliyeNo,
        },
        logoRef: directSqlResult.logoRef,
        ficheNo,
        irsaliyeNo,
      }
    }
  }
  catch (error: any) {
    consola.error('Fatura oluşturulurken beklenmeyen bir hata meydana geldi:', error)
    return {
      status: 'error',
      error: error.message || 'Fatura işlenirken beklenmeyen bir hata oluştu.',
    }
  }
}

const SatisFaturalariService = {
  handleCreateInvoice,
  transformToLogoFormat,
  convertTimeToLogoInt,
}

export default SatisFaturalariService
