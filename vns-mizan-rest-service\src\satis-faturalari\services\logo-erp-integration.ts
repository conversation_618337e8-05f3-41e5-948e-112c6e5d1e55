import { consola } from 'consola'
import sql from 'mssql'
import { randomUUID } from 'node:crypto'
import DbService from '../../shared/services/db-service.ts'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import type { SatisFaturaHeader, SatisFaturaLineItem } from '../models/satis-fatura.ts'

/**
 * Service for direct Logo ERP integration operations
 * Contains the actual implementations for inserting into Logo ERP tables
 */
const LogoErpIntegration = {
  /**
   * Insert into actual Logo INVOICE table (LG_{FFF}_{DD}_INVOICE)
   */
  insertLogoActualInvoice: async ({
    invoice,
    veritabaniId,
    requestData,
    totals,
    logoCredentials
  }: {
    invoice: SatisFaturaHeader
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId
        })
      }

      // Generate a unique fiche number if not provided
      let ficheno = invoice.NUMBER
      if (invoice.NUMBER === '~') {
        // Check if a format is provided
        const format = requestData?.fatura_numarasi_formati

        if (format && format.includes('_')) {
          // Use formatted fiche number generation
          ficheno = await LogoLookupService.generateFormattedFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            format,
            veritabaniId,
          })
        }
        else {
          // Use default fiche number generation
          ficheno = await LogoLookupService.generateNewFicheNo({
            trcode: invoice.TYPE,
            tableName: 'INVOICE',
            veritabaniId,
          })
        }
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided
      const totalVat = totals?.totalVat || 0
      const totalNet = totals?.totalNet || 0
      const totalGross = totals?.totalGross || 0
      const totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(invoice.CURR_INVOICE, veritabaniId, invoice.DATE)
        tcXrate = exchangeRate?.TC_XRATE || 1
        rcXrate = exchangeRate?.RC_XRATE || 1
      }

      // Map invoice fields to Logo INVOICE table structure
      const result = await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, invoice.TYPE)
        .input('FICHENO', sql.VarChar(17), ficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('TIME_', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE)
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('CLIENTREF', sql.Int, clientRef)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('ACCOUNTREF', sql.Int, null) // Account reference
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('ACCOUNTED', sql.SmallInt, 0)
        .input('PAIDINCASH', sql.SmallInt, 0)
        .input('FROMKASA', sql.SmallInt, 0)
        .input('ENTEGSET', sql.SmallInt, 0)
        .input('VAT', sql.SmallInt, 0)
        .input('ADDDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTS', sql.Float, 0)
        .input('TOTALDISCOUNTED', sql.Float, totalDiscounted)
        .input('ADDEXPENSES', sql.Float, 0)
        .input('TOTALEXPENSES', sql.Float, 0)
        .input('DISTEXPENSE', sql.Float, 0)
        .input('TOTALDEPOZITO', sql.Float, 0)
        .input('TOTALPROMOTIONS', sql.Float, 0)
        .input('VATINCGROSS', sql.SmallInt, 1)
        .input('TOTALVAT', sql.Float, totalVat)
        .input('GROSSTOTAL', sql.Float, totalGross)
        .input('NETTOTAL', sql.Float, totalNet)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1)
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2)
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3)
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4)
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5)
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6)
        .input('INTERESTAPP', sql.SmallInt, 0)
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('TRNET', sql.Float, totalNet / tcXrate)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('REPORTNET', sql.Float, totalNet / rcXrate)
        .input('ONLYONEPAYLINE', sql.SmallInt, 0)
        .input('KASTRANSREF', sql.Int, 0)
        .input('PAYDEFREF', sql.Int, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('GVATINC', sql.SmallInt, 1)
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('ACCFICHEREF', sql.Int, 0)
        .input('ADDEXPACCREF', sql.Int, 0)
        .input('ADDEXPCENTREF', sql.Int, 0)
        .input('DECPRDIFF', sql.Float, 0)
        .input('SALESMANREF', sql.Int, salesmanRef)
        .input('PROJECTREF', sql.Int, projectRef)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('DOCDATE', sql.DateTime, invoice.DOC_DATE ? new Date(invoice.DOC_DATE) : new Date(invoice.DATE))
        .input('PROFILEID', sql.SmallInt, invoice.PROFILE_ID || 2)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREATEDDATE', sql.DateTime, now)
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_INVOICE (
            GRPCODE, TRCODE, FICHENO, DATE_, TIME_, DOCODE, SPECODE, CYPHCODE,
            CLIENTREF, RECVREF, CENTERREF, ACCOUNTREF, SOURCEINDEX, SOURCECOSTGRP,
            CANCELLED, ACCOUNTED, PAIDINCASH, FROMKASA, ENTEGSET, VAT,
            ADDDISCOUNTS, TOTALDISCOUNTS, TOTALDISCOUNTED, ADDEXPENSES, TOTALEXPENSES,
            DISTEXPENSE, TOTALDEPOZITO, TOTALPROMOTIONS, VATINCGROSS, TOTALVAT,
            GROSSTOTAL, NETTOTAL, GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6,
            INTERESTAPP, TRCURR, TRRATE, TRNET, REPORTRATE, REPORTNET,
            ONLYONEPAYLINE, KASTRANSREF, PAYDEFREF, PRINTCNT, GVATINC, BRANCH, DEPARTMENT,
            ACCFICHEREF, ADDEXPACCREF, ADDEXPCENTREF, DECPRDIFF,
            SALESMANREF, PROJECTREF, FACTORYNR, DOCDATE, PROFILEID, GUID,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREATEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC
          )
          OUTPUT INSERTED.LOGICALREF, INSERTED.FICHENO
          VALUES (
            @GRPCODE, @TRCODE, @FICHENO, @DATE_, @TIME_, @DOCODE, @SPECODE, @CYPHCODE,
            @CLIENTREF, @RECVREF, @CENTERREF, @ACCOUNTREF, @SOURCEINDEX, @SOURCECOSTGRP,
            @CANCELLED, @ACCOUNTED, @PAIDINCASH, @FROMKASA, @ENTEGSET, @VAT,
            @ADDDISCOUNTS, @TOTALDISCOUNTS, @TOTALDISCOUNTED, @ADDEXPENSES, @TOTALEXPENSES,
            @DISTEXPENSE, @TOTALDEPOZITO, @TOTALPROMOTIONS, @VATINCGROSS, @TOTALVAT,
            @GROSSTOTAL, @NETTOTAL, @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6,
            @INTERESTAPP, @TRCURR, @TRRATE, @TRNET, @REPORTRATE, @REPORTNET,
            @ONLYONEPAYLINE, @KASTRANSREF, @PAYDEFREF, @PRINTCNT, @GVATINC, @BRANCH, @DEPARTMENT,
            @ACCFICHEREF, @ADDEXPACCREF, @ADDEXPCENTREF, @DECPRDIFF,
            @SALESMANREF, @PROJECTREF, @FACTORYNR, @DOCDATE, @PROFILEID, @GUID,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREATEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC
          )
        `)

      if (result.recordset.length > 0 && result.recordset[0]) {
        const insertedRecord = result.recordset[0]
        consola.success(`Logo INVOICE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO
        }
      }

      throw new Error('INVOICE tablosuna ekleme başarısız oldu - OUTPUT döndürülmedi')
    }
    catch (error) {
      consola.error('Logo INVOICE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },

  /**
   * Insert into actual Logo STFICHE table (LG_{FFF}_{DD}_STFICHE)
   */
  insertLogoActualStfiche: async ({
    invoice,
    invoiceRef,
    ficheNo,
    veritabaniId,
    requestData,
    totals,
    logoCredentials
  }: {
    invoice: SatisFaturaHeader
    invoiceRef: number
    ficheNo: string
    veritabaniId: string
    requestData?: any
    totals?: {
      totalVat: number
      totalNet: number
      totalGross: number
      totalDiscounted: number
    }
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{ logicalref?: number, ficheno?: string }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
      const request = logoConnection.request()

      // Get client reference from ARP_CODE
      let clientRef = null
      if (invoice.ARP_CODE) {
        const clientInfo = await LogoLookupService.getClientRefFromCode(invoice.ARP_CODE, veritabaniId)
        clientRef = clientInfo?.logicalref
      }

      // Resolve PROJECTREF from project_code if provided
      let projectRef = null
      if (invoice.PROJECT_CODE) {
        projectRef = await LogoLookupService.getProjectRefFromCode(invoice.PROJECT_CODE, veritabaniId)
      }

      // Resolve SALESMANREF from salesman_code if provided
      let salesmanRef = null
      if (invoice.SALESMAN_CODE) {
        salesmanRef = await LogoLookupService.getSalesmanRefFromCode(invoice.SALESMAN_CODE, veritabaniId)
      }

      // Get user reference from credentials if provided
      let userRef = null
      if (logoCredentials?.kullanici_adi) {
        userRef = await LogoLookupService.getUserRefFromCapiuser({
          kullaniciAdi: logoCredentials.kullanici_adi,
          veritabaniId
        })
      }

      // Generate a unique stfiche number
      let stficheno
      // Check if a format is provided for irsaliye_no
      const irsaliyeFormat = requestData?.irsaliye_numarasi_formati

      if (irsaliyeFormat && irsaliyeFormat.includes('_')) {
        // Use formatted fiche number generation
        stficheno = await LogoLookupService.generateFormattedFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          format: irsaliyeFormat,
          veritabaniId,
        })
      }
      else {
        // Use default fiche number generation
        stficheno = await LogoLookupService.generateNewFicheNo({
          trcode: 7, // 7 for STFICHE
          tableName: 'STFICHE',
          veritabaniId,
        })
      }

      // Get current date/time for CAPIBLOCK fields
      const now = new Date()
      const currentDate = now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate()
      const currentHour = now.getHours()
      const currentMin = now.getMinutes()
      const currentSec = now.getSeconds()

      // Calculate totals if not provided
      const totalVat = totals?.totalVat || 0
      const totalNet = totals?.totalNet || 0
      const totalGross = totals?.totalGross || 0
      const totalDiscounted = totals?.totalDiscounted || 0

      // Get exchange rates
      let tcXrate = 1
      let rcXrate = 1
      if (invoice.CURR_INVOICE && invoice.CURR_INVOICE !== 0) {
        const exchangeRate = await LogoLookupService.getExchangeRate(invoice.CURR_INVOICE, veritabaniId, invoice.DATE)
        tcXrate = exchangeRate?.TC_XRATE || 1
        rcXrate = exchangeRate?.RC_XRATE || 1
      }

      // Map invoice fields to Logo STFICHE table structure
      const result = await request
        .input('GRPCODE', sql.SmallInt, 2) // 2 is for sales invoices
        .input('TRCODE', sql.SmallInt, 7) // 7 is for sales invoice in STFICHE
        .input('IOCODE', sql.SmallInt, 3) // 3 is for output
        .input('FICHENO', sql.VarChar(17), stficheno)
        .input('DATE_', sql.DateTime, new Date(invoice.DATE))
        .input('FTIME', sql.Int, invoice.TIME)
        .input('DOCODE', sql.VarChar(33), invoice.DOC_NUMBER)
        .input('INVNO', sql.VarChar(17), ficheNo)
        .input('SPECODE', sql.VarChar(11), invoice.AUXIL_CODE)
        .input('CYPHCODE', sql.VarChar(11), '') // Cipher Code
        .input('INVOICEREF', sql.Int, invoiceRef)
        .input('CLIENTREF', sql.Int, clientRef)
        .input('RECVREF', sql.Int, 0) // Receiver reference
        .input('ACCOUNTREF', sql.Int, null) // Account reference
        .input('CENTERREF', sql.Int, 0) // Center reference
        .input('PRODORDERREF', sql.Int, 0) // Production order reference
        .input('PORDERFICHENO', sql.VarChar(17), '') // Production order fiche number
        .input('SOURCETYPE', sql.SmallInt, 0)
        .input('SOURCEINDEX', sql.SmallInt, invoice.SOURCE_WH || 0)
        .input('SOURCEWSREF', sql.Int, 0)
        .input('SOURCEPOLNREF', sql.Int, 0)
        .input('SOURCECOSTGRP', sql.SmallInt, invoice.SOURCE_COST_GRP || 0)
        .input('DESTTYPE', sql.SmallInt, 0)
        .input('DESTINDEX', sql.SmallInt, 0)
        .input('DESTWSREF', sql.Int, 0)
        .input('DESTPOLNREF', sql.Int, 0)
        .input('DESTCOSTGRP', sql.SmallInt, 0)
        .input('FACTORYNR', sql.SmallInt, invoice.FACTORY || 0)
        .input('BRANCH', sql.SmallInt, invoice.DIVISION || 0)
        .input('DEPARTMENT', sql.SmallInt, invoice.DEPARTMENT || 0)
        .input('GENEXP1', sql.VarChar(51), invoice.NOTES1)
        .input('GENEXP2', sql.VarChar(51), invoice.NOTES2)
        .input('GENEXP3', sql.VarChar(51), invoice.NOTES3)
        .input('GENEXP4', sql.VarChar(51), invoice.NOTES4)
        .input('GENEXP5', sql.VarChar(51), invoice.NOTES5)
        .input('GENEXP6', sql.VarChar(51), invoice.NOTES6)
        .input('CANCELLED', sql.SmallInt, 0)
        .input('CLOSED', sql.SmallInt, 0)
        .input('PRINTCNT', sql.SmallInt, 0)
        .input('TRCURR', sql.SmallInt, invoice.CURR_INVOICE || 0)
        .input('TRRATE', sql.Float, tcXrate)
        .input('REPORTRATE', sql.Float, rcXrate)
        .input('SALESMANREF', sql.Int, salesmanRef)
        .input('PROJECTREF', sql.Int, projectRef)
        .input('GUID', sql.VarChar(37), randomUUID())
        .input('DOCDATE', sql.DateTime, invoice.DOC_DATE ? new Date(invoice.DOC_DATE) : new Date(invoice.DATE))
        .input('DOCTIME', sql.Int, invoice.TIME)
        .input('CAPIBLOCK_CREATEDBY', sql.SmallInt, userRef || 0)
        .input('CAPIBLOCK_CREATEDDATE', sql.DateTime, now)
        .input('CAPIBLOCK_CREATEDHOUR', sql.SmallInt, currentHour)
        .input('CAPIBLOCK_CREATEDMIN', sql.SmallInt, currentMin)
        .input('CAPIBLOCK_CREATEDSEC', sql.SmallInt, currentSec)
        .input('CAPIBLOCK_MODIFIEDBY', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDDATE', sql.DateTime, null)
        .input('CAPIBLOCK_MODIFIEDHOUR', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDMIN', sql.SmallInt, 0)
        .input('CAPIBLOCK_MODIFIEDSEC', sql.SmallInt, 0)
        .query(`
          INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STFICHE (
            GRPCODE, TRCODE, IOCODE, FICHENO, DATE_, FTIME, DOCODE, INVNO, SPECODE, CYPHCODE,
            INVOICEREF, CLIENTREF, RECVREF, ACCOUNTREF, CENTERREF, PRODORDERREF, PORDERFICHENO,
            SOURCETYPE, SOURCEINDEX, SOURCEWSREF, SOURCEPOLNREF, SOURCECOSTGRP, DESTTYPE,
            DESTINDEX, DESTWSREF, DESTPOLNREF, DESTCOSTGRP, FACTORYNR, BRANCH, DEPARTMENT,
            GENEXP1, GENEXP2, GENEXP3, GENEXP4, GENEXP5, GENEXP6, CANCELLED, CLOSED, PRINTCNT,
            TRCURR, TRRATE, REPORTRATE, SALESMANREF, PROJECTREF, GUID, DOCDATE, DOCTIME,
            CAPIBLOCK_CREATEDBY, CAPIBLOCK_CREATEDDATE, CAPIBLOCK_CREATEDHOUR,
            CAPIBLOCK_CREATEDMIN, CAPIBLOCK_CREATEDSEC, CAPIBLOCK_MODIFIEDBY,
            CAPIBLOCK_MODIFIEDDATE, CAPIBLOCK_MODIFIEDHOUR, CAPIBLOCK_MODIFIEDMIN,
            CAPIBLOCK_MODIFIEDSEC
          )
          OUTPUT INSERTED.LOGICALREF, INSERTED.FICHENO
          VALUES (
            @GRPCODE, @TRCODE, @IOCODE, @FICHENO, @DATE_, @FTIME, @DOCODE, @INVNO, @SPECODE, @CYPHCODE,
            @INVOICEREF, @CLIENTREF, @RECVREF, @ACCOUNTREF, @CENTERREF, @PRODORDERREF, @PORDERFICHENO,
            @SOURCETYPE, @SOURCEINDEX, @SOURCEWSREF, @SOURCEPOLNREF, @SOURCECOSTGRP, @DESTTYPE,
            @DESTINDEX, @DESTWSREF, @DESTPOLNREF, @DESTCOSTGRP, @FACTORYNR, @BRANCH, @DEPARTMENT,
            @GENEXP1, @GENEXP2, @GENEXP3, @GENEXP4, @GENEXP5, @GENEXP6, @CANCELLED, @CLOSED, @PRINTCNT,
            @TRCURR, @TRRATE, @REPORTRATE, @SALESMANREF, @PROJECTREF, @GUID, @DOCDATE, @DOCTIME,
            @CAPIBLOCK_CREATEDBY, @CAPIBLOCK_CREATEDDATE, @CAPIBLOCK_CREATEDHOUR,
            @CAPIBLOCK_CREATEDMIN, @CAPIBLOCK_CREATEDSEC, @CAPIBLOCK_MODIFIEDBY,
            @CAPIBLOCK_MODIFIEDDATE, @CAPIBLOCK_MODIFIEDHOUR, @CAPIBLOCK_MODIFIEDMIN,
            @CAPIBLOCK_MODIFIEDSEC
          )
        `)

      if (result.recordset.length > 0 && result.recordset[0]) {
        const insertedRecord = result.recordset[0]
        consola.success(`Logo STFICHE tablosuna başarıyla eklendi. LOGICALREF: ${insertedRecord.LOGICALREF}, FICHENO: ${insertedRecord.FICHENO}`)
        return {
          logicalref: insertedRecord.LOGICALREF,
          ficheno: insertedRecord.FICHENO
        }
      }

      throw new Error('STFICHE tablosuna ekleme başarısız oldu - OUTPUT döndürülmedi')
    }
    catch (error) {
      consola.error('Logo STFICHE tablosuna ekleme sırasında hata oluştu:', error)
      throw error
    }
  },

  /**
   * Insert into actual Logo STLINE table (LG_{FFF}_{DD}_STLINE)
   */
  insertLogoActualStlines: async ({
    invoiceRef,
    stficheRef,
    lines,
    veritabaniId,
    invoiceDate,
  }: {
    invoiceRef: number
    stficheRef: number
    lines: SatisFaturaLineItem[]
    veritabaniId: string
    invoiceDate: string
  }): Promise<boolean> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const logoConnection = await DbService.getLogoConnectionById(veritabaniId)

      consola.info(`STLINE ekleme başlatılıyor. invoiceRef: ${invoiceRef}, satır sayısı: ${lines.length}`)

      let lineNo = 0
      for (const line of lines) {
        lineNo++

        // Get stock reference from MASTER_CODE
        let stockRef = null
        if (line.MASTER_CODE) {
          stockRef = await LogoLookupService.getItemRefFromCode(line.MASTER_CODE, veritabaniId)
        }

        // Resolve PROJECTREF from project_code if provided
        let projectRef = null
        if (line.PROJECT_CODE) {
          projectRef = await LogoLookupService.getProjectRefFromCode(line.PROJECT_CODE, veritabaniId)
        }

        // Resolve SALESMANREF from salesman_code if provided
        let salesmanRef = null
        if (line.SALEMANCODE) {
          salesmanRef = await LogoLookupService.getSalesmanRefFromCode(line.SALEMANCODE, veritabaniId)
        }

        // Get unit reference from UNIT_CODE
        let uomRef = null
        if (line.UNIT_CODE) {
          uomRef = await LogoLookupService.getUnitRefFromCode(line.UNIT_CODE, veritabaniId)
        }

        // Calculate line totals
        const quantity = line.QUANTITY || 0
        const price = line.PRICE || 0
        const total = line.TOTAL || (quantity * price)
        const vatRate = line.VAT_RATE || 0
        const vatAmount = total * (vatRate / 100)
        const lineNet = total + vatAmount

        // Get exchange rates
        let tcXrate = 1
        let rcXrate = 1
        if (line.EDT_CURR && line.EDT_CURR !== 0) {
          const exchangeRate = await LogoLookupService.getExchangeRate(line.EDT_CURR, veritabaniId, invoiceDate)
          tcXrate = exchangeRate?.TC_XRATE || 1
          rcXrate = exchangeRate?.RC_XRATE || 1
        }

        // Insert the line
        const request = logoConnection.request()
        await request
          .input('STOCKREF', sql.Int, stockRef)
          .input('LINETYPE', sql.SmallInt, 0) // 0 based on example
          .input('PREVLINEREF', sql.Int, 0)
          .input('PREVLINENO', sql.SmallInt, 0)
          .input('DETLINE', sql.SmallInt, 0)
          .input('TRCODE', sql.SmallInt, 7) // 7 for sales invoice
          .input('DATE_', sql.DateTime, new Date(invoiceDate))
          .input('FTIME', sql.Int, 185999616) // Based on example
          .input('GLOBTRANS', sql.SmallInt, 0)
          .input('CALCTYPE', sql.SmallInt, 0)
          .input('PRODORDERREF', sql.Int, 0)
          .input('SOURCETYPE', sql.SmallInt, 0)
          .input('SOURCEINDEX', sql.SmallInt, line.SOURCEINDEX || 0)
          .input('SOURCECOSTGRP', sql.SmallInt, line.SOURCE_COST_GRP || 0)
          .input('DESTINDEX', sql.SmallInt, 0)
          .input('DESTCOSTGRP', sql.SmallInt, 0)
          .input('IOCODE', sql.SmallInt, 3) // 3 for output
          .input('STFICHEREF', sql.Int, stficheRef)
          .input('INVOICEREF', sql.Int, invoiceRef)
          .input('AMOUNT', sql.Float, quantity)
          .input('PRICE', sql.Float, price)
          .input('TOTAL', sql.Float, total)
          .input('CANCELLED', sql.SmallInt, 0)
          .input('LINENET', sql.Float, lineNet)
          .input('DISTCOST', sql.Float, 0)
          .input('DISTDISC', sql.Float, 0)
          .input('DISTEXP', sql.Float, 0)
          .input('DISTPROM', sql.Float, 0)
          .input('DISCPER', sql.Float, line.DISCOUNT_RATE || 0)
          .input('LINEEXP', sql.Float, 0)
          .input('UOMREF', sql.SmallInt, uomRef)
          .input('USREF', sql.SmallInt, 0)
          .input('UINFO1', sql.Float, 0)
          .input('UINFO2', sql.Float, 0)
          .input('PLNAMOUNT', sql.Float, quantity)
          .input('VATRATE', sql.Float, vatRate)
          .input('VATAMNT', sql.Float, vatAmount)
          .input('VATMATRAH', sql.Float, total)
          .input('BILLEDITEM', sql.SmallInt, line.BILLED || 1)
          .input('BILLED', sql.SmallInt, line.BILLED || 1)
          .input('VATINC', sql.SmallInt, line.VAT_INCLUDED || 0)
          .input('FACTORYNR', sql.SmallInt, line.FACTORY || 0)
          .input('SPECODE', sql.VarChar(11), line.AUXIL_CODE)
          .input('PROJECTREF', sql.Int, projectRef)
          .input('CENTERREF', sql.Int, 0)
          .input('GUID', sql.VarChar(37), randomUUID())
          .query(`
            INSERT INTO LG_${logoConfig.erp.firma_numarasi}_${logoConfig.erp.donem_numarasi}_STLINE (
              STOCKREF, LINETYPE, PREVLINEREF, PREVLINENO, DETLINE, TRCODE, DATE_, FTIME,
              GLOBTRANS, CALCTYPE, SOURCEINDEX, SOURCECOSTGRP, DESTINDEX, DESTCOSTGRP,
              IOCODE, STFICHEREF, INVOICEREF, AMOUNT, PRICE, TOTAL, CANCELLED, LINENET,
              DISTCOST, DISTDISC, DISTEXP, DISTPROM, DISCPER, LINEEXP, UOMREF, USREF,
              UINFO1, UINFO2, PLNAMOUNT, VATRATE, VATAMNT, VATMATRAH, BILLEDITEM, BILLED,
              VATINC, FACTORYNR, SPECODE, PROJECTREF, CENTERREF, GUID
            )
            VALUES (
              @STOCKREF, @LINETYPE, @PREVLINEREF, @PREVLINENO, @DETLINE, @TRCODE, @DATE_, @FTIME,
              @GLOBTRANS, @CALCTYPE, @SOURCEINDEX, @SOURCECOSTGRP, @DESTINDEX, @DESTCOSTGRP,
              @IOCODE, @STFICHEREF, @INVOICEREF, @AMOUNT, @PRICE, @TOTAL, @CANCELLED, @LINENET,
              @DISTCOST, @DISTDISC, @DISTEXP, @DISTPROM, @DISCPER, @LINEEXP, @UOMREF, @USREF,
              @UINFO1, @UINFO2, @PLNAMOUNT, @VATRATE, @VATAMNT, @VATMATRAH, @BILLEDITEM, @BILLED,
              @VATINC, @FACTORYNR, @SPECODE, @PROJECTREF, @CENTERREF, @GUID
            )
          `)

        consola.info(`STLINE satırı eklendi. Satır: ${lineNo}, STOCKREF: ${stockRef}`)
      }

      consola.success(`Tüm STLINE satırları başarıyla eklendi. Toplam: ${lines.length}`)
      return true
    }
    catch (error) {
      consola.error('Logo STLINE tablosuna ekleme sırasında hata oluştu:', error)
      return false
    }
  },
}

export default LogoErpIntegration
