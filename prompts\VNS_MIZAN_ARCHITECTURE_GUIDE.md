# VNS Mizan Architecture Guide

Bu dokümantasyon, VNS Mizan REST Service'in mimari desenlerini ve program mantığını gelecekte çalışacak AI agent'lar i<PERSON><PERSON> açıklamaktadır.

## Service Layer Architecture

### 1. Logo REST Service Pattern (Pure Logo ERP REST API Communication)

**Amaç:** Sadece Logo ERP REST API ile iletişim kurmak
**Sorumluluk:** Logo ERP REST API operasyonları
**YAPMAZ:** Uygulama veritabanı işlemleri, loglama

```typescript
// ✅ Doğru Logo REST Service implementasyonu
export const LogoRestService = {
  postCariHesap: async ({ 
    accessToken, 
    cariHesap, 
    veritabaniId 
  }: { 
    accessToken: string
    cariHesap: LogoCariHesapRequest
    veritabaniId: string 
  }): Promise<{ INTERNAL_REFERENCE: number }> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const response = await fetch(`${logoConfig.erp.rest_settings.rest_api_url}/Arps`, {
        method: 'POST',
        body: JSON.stringify(cariHesap),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      const data = await response.json()
      if (!response.ok) {
        throw new Error(`HTTP hatası! Durum: ${response.status}`)
      }

      return data // Sadece Logo'dan gelen veriyi döndür
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası:', error)
      throw error // Hata yönetimi basit tut
    }
  }
}
```

**❌ Yanlış Yaklaşım:**
```typescript
// Logo REST Service'de loglama YAPMAYIN
return {
  INTERNAL_REFERENCE: data.INTERNAL_REFERENCE,
  logoRestLogData: createLogoRestLogData(...) // ❌ YANLIŞ
}
```

### 2. Logo SQL Service Pattern (Pure Logo ERP Database Operations)

**Amaç:** Sadece Logo ERP veritabanı işlemleri
**Sorumluluk:** Logo ERP veritabanına CRUD operasyonları
**YAPMAZ:** Uygulama veritabanı işlemleri, loglama

```typescript
// ✅ Doğru Logo SQL Service implementasyonu
export const LogoSqlService = {
  insertClCard: async ({ 
    veritabaniId, 
    params 
  }: { 
    veritabaniId: string
    params: ClCardParams 
  }) => {
    const logoConnection = await DbService.getLogoConnectionById(veritabaniId)
    const request = logoConnection.request()
    
    // Parametreleri set et
    Object.entries(params).forEach(([key, value]) => {
      request.input(key, value)
    })

    // Logo ERP veritabanına insert
    const result = await request.query(insertQuery)
    return result.recordset[0].LOGICALREF // Sadece Logo ref döndür
  }
}
```

**❌ Yanlış Yaklaşım:**
```typescript
// Logo SQL Service'de loglama YAPMAYIN
return {
  logoRef: result.recordset[0].LOGICALREF,
  logoSqlLogData: createLogoSqlLogData(...) // ❌ YANLIŞ
}
```

### 3. Main Business Service Pattern (Application Business Logic)

**Amaç:** Ana iş mantığı, koordinasyon ve loglama
**Sorumluluk:** İş kuralları, uygulama veritabanı işlemleri, loglama koordinasyonu
**YAPAR:** Logo servislerini çağırır, sonuçları loglar, uygulama veritabanına kaydeder

```typescript
// ✅ Doğru Main Business Service implementasyonu
export async function sendCariHesap({
  cariHesapData,
  veritabaniId,
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
}) {
  const logoConfig = await getLogoConfigById(veritabaniId)
  
  if (logoConfig.erp.rest_settings.use_rest) {
    return await processWithRest({ cariHesapData, veritabaniId })
  } else {
    return await processWithoutRest({ cariHesapData, veritabaniId })
  }
}

async function processWithRest({ cariHesapData, veritabaniId }) {
  // 1. Uygulama veritabanına kaydet (logoRef olmadan)
  const cardId = await insertCariHesaplar({ 
    cariHesapData, 
    veritabaniId, 
    logicalref: 0 
  })
  
  let logoRestLogData = ''
  let logicalref = null
  
  try {
    // 2. Logo REST API'yi çağır
    const readyCustomerToPost = await transformToLogoFormat({ cariHesapData })
    const accessToken = await LogoRestService.getToken({ veritabaniId })
    const response = await LogoRestService.postCariHesap({
      accessToken,
      cariHesap: readyCustomerToPost,
      veritabaniId,
    })
    
    logicalref = response.INTERNAL_REFERENCE
    
    // 3. REST loglama verisi oluştur (inline)
    logoRestLogData = createLogoRestLogData({
      endpoint: '/Arps',
      method: 'POST',
      payload: readyCustomerToPost,
      responseStatus: 200,
      responseData: response
    })
    
    // 4. Başarılı durumda güncelle
    await updateCariHesapWithLogData({ 
      cardId, 
      veritabaniId, 
      logoRestLogData,
      logoRef: logicalref
    })
    
  } catch (error) {
    // 5. Hata durumunda da logla
    const errorLogData = createLogoRestLogData({
      endpoint: '/Arps',
      method: 'POST',
      payload: readyCustomerToPost || null,
      error: error instanceof Error ? error.message : 'Bilinmeyen hata'
    })
    
    await updateCariHesapWithLogData({ 
      cardId, 
      veritabaniId, 
      logoRestLogData: errorLogData 
    })
    
    throw error
  }
}

async function processWithoutRest({ cariHesapData, veritabaniId }) {
  const logoConfig = await getLogoConfigById(veritabaniId)
  const clcardParams = await transformToClCardFormat({ cariHesapData })
  
  // 1. Logo SQL işlemi
  const logicalref = await LogoSqlService.insertClCard({ 
    veritabaniId, 
    params: clcardParams 
  })
  
  // 2. SQL loglama verisi oluştur (inline)
  let logoSqlLogData: string
  try {
    const firmaNo = logoConfig.erp.firma_numarasi
    const timestamp = new Date().toISOString()
    
    const logData = {
      [`LG_${firmaNo}_CLCARD`]: {
        ...clcardParams,
        operation: 'INSERT',
        timestamp
      }
    }
    
    logoSqlLogData = JSON.stringify(logData, null, 2)
  } catch (error) {
    const errorLogData = {
      error: `Logo SQL log data oluşturulurken hata: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
      timestamp: new Date().toISOString(),
      original_data: clcardParams
    }
    logoSqlLogData = JSON.stringify(errorLogData, null, 2)
  }
  
  // 3. Uygulama veritabanına kaydet
  await insertCariHesaplar({ 
    cariHesapData, 
    veritabaniId, 
    logicalref,
    logoSqlLogData 
  })
}
```

## Logging Architecture

### GET vs POST İşlemleri Ayrımı

#### GET İşlemleri (Okuma Operasyonları)
- **Loglama GEREKMEZ** - Sadece Logo ERP'den okuma yapılır
- **Uygulama Veritabanı Tabloları GEREKMEZ** - Doğrudan Logo ERP'den yanıt
- **Örnekler:** stoklar, reyonlar, kategoriler, markalar, ana-gruplar, alt-gruplar

#### POST İşlemleri (Yazma Operasyonları)
- **Loglama GEREKLİ** - Audit trail ve hata kurtarma için
- **Uygulama Veritabanı Tabloları GEREKLİ** - İş verisi ve loglama için
- **Örnekler:** cariler, satis-faturalari, satinalma-faturalari

### Loglama Nerede Yapılır (Sadece POST İşlemleri İçin)
- ✅ **Main Business Services'de** (örn: `cari-hesaplar-service.ts`)
- ❌ **Logo Services'de DEĞİL** (logo-rest-service.ts, logo-sql-service.ts)

### Neyi Logluyoruz (Sadece POST İşlemleri İçin)
1. **logo_rest_data:** REST API payload'ları ve yanıtları
2. **logo_sql_data:** Logo veritabanı operasyonları

### Loglama Nasıl Implement Ediliyor

#### 1. Inline JSON Creation (Utility Functions Kullanmayın)
```typescript
// ✅ Doğru - Inline loglama
const logoRestLogData = createLogoRestLogData({
  endpoint: '/Arps',
  method: 'POST',
  payload: readyCustomerToPost,
  responseStatus: 200,
  responseData: response
})

function createLogoRestLogData({ endpoint, method, payload, responseStatus, responseData, error }) {
  const logData = {
    endpoint,
    method,
    timestamp: new Date().toISOString(),
    payload,
    response_status: responseStatus,
    response_data: responseData,
    error
  }
  return JSON.stringify(logData, null, 2)
}
```

#### 2. Logo Table Naming Convention
```typescript
// Logo SQL loglama için tablo adı formatı
const logData = {
  [`LG_${firmaNo}_CLCARD`]: {        // Dönem numarası olmayan tablo
    ...clcardParams,
    operation: 'INSERT',
    timestamp: new Date().toISOString()
  },
  [`LG_${firmaNo}_${donemNo}_INVOICE`]: {  // Dönem numarası olan tablo
    ...invoiceParams,
    operation: 'INSERT',
    timestamp: new Date().toISOString()
  }
}
```

#### 3. Database Storage
```typescript
// Uygulama veritabanına loglama verisi kaydetme
const dbConnection = await DbService.getConnection('db')
const request = dbConnection.request()
request.input('logo_rest_data', logoRestLogData || null)
request.input('logo_sql_data', logoSqlLogData || null)

await request.query(`
  INSERT INTO CariHesaplar (
    -- ... diğer alanlar
    logo_rest_data, 
    logo_sql_data,
    -- ...
  ) VALUES (
    -- ... diğer değerler
    @logo_rest_data, 
    @logo_sql_data,
    -- ...
  )
`)
```

## Route and API Architecture

### Route Organization
```
src/
├── cariler/
│   ├── routes/
│   │   └── cariler-routes.ts      # API endpoint tanımları
│   ├── services/
│   │   ├── cari-hesaplar-service.ts  # Ana iş mantığı
│   │   ├── logo-rest-service.ts      # Logo REST API
│   │   └── logo-sql-service.ts       # Logo SQL
│   └── models/
│       └── types.ts               # Tip tanımları
```

### Request Flow
```
1. Route (cariler-routes.ts)
   ↓
2. Main Business Service (cari-hesaplar-service.ts)
   ↓
3. Logo Services (logo-rest-service.ts veya logo-sql-service.ts)
   ↓
4. Database Operations (Logo ERP veya Application DB)
```

### Error Handling
- **Turkish Language:** Tüm hata mesajları Türkçe
- **Comprehensive Logging:** Tüm hatalar loglanır
- **Graceful Degradation:** REST API başarısız olursa SQL'e geçiş

### Multi-Tenant Support
```typescript
// veritabani_id ile routing
export async function sendCariHesap({
  cariHesapData,
  veritabaniId  // Bu parametre ile hangi Logo ERP'ye gideceğini belirliyoruz
}: {
  cariHesapData: CariHesapRequest
  veritabaniId: string
}) {
  // 1. veritabani_id ile Logo konfigürasyonunu al
  const logoConfig = await getLogoConfigById(veritabaniId)
  
  // 2. Bu konfigürasyona göre entegrasyon yöntemini seç
  if (logoConfig.erp.rest_settings.use_rest) {
    return await processWithRest({ cariHesapData, veritabaniId })
  } else {
    return await processWithoutRest({ cariHesapData, veritabaniId })
  }
}
```

## Database Architecture

### Application Database (VnsMizanEntegrasyonDb)
- **Transactional Business Data:** Sadece POST işlemleri için iş verisi + loglama sütunları
- **Logging Columns:** Sadece yazma operasyonu yapan tablolarda `logo_rest_data` ve `logo_sql_data`
- **Table Creation:** Doğrudan CREATE TABLE tanımları, migration yok
- **Read-Only Data:** GET işlemleri için tablo oluşturulmaz, doğrudan Logo ERP'den okunur

### Logo ERP Databases
- **External Access:** Logo services aracılığıyla erişim
- **Multi-Tenant:** Her `veritabani_id` farklı Logo ERP'ye işaret eder
- **Table Naming:** `LG_{firma_no}_{donem_no}_{table_name}` formatı
- **Read Operations:** Stoklar, reyonlar, kategoriler vb. doğrudan Logo ERP'den okunur
- **Write Operations:** Cariler, faturalar vb. Logo ERP'ye yazılır ve uygulama DB'de loglanır

### Logging Columns Standard (Sadece POST İşlemleri İçin)
```sql
-- Sadece yazma operasyonu yapan iş modülü tablolarında zorunlu
logo_rest_data VARCHAR(MAX) NULL,
logo_sql_data VARCHAR(MAX) NULL
```

### Mimari Prensip
- **GET İşlemleri:** Logo ERP → Doğrudan yanıt (uygulama veritabanı saklaması yok)
- **POST İşlemleri:** Mizan → Uygulama DB (loglama ile) → Logo ERP → Uygulama DB'yi sonuçlarla güncelle

### Table Creation Strategy
```sql
-- ✅ Doğru - Ana tablo tanımında
CREATE TABLE YeniModulTablosu (
    id INT PRIMARY KEY IDENTITY(1,1),
    
    -- İş alanları
    alan1 VARCHAR(50),
    alan2 INT,
    
    -- Sistem alanları
    veritabani_id VARCHAR(37),
    error NVARCHAR(MAX),
    
    -- Logo loglama alanları (ZORUNLU)
    logo_rest_data VARCHAR(MAX) NULL,
    logo_sql_data VARCHAR(MAX) NULL,
    
    -- Zaman damgaları
    createdAt DATETIME2 NOT NULL DEFAULT GETDATE(),
    updatedAt DATETIME2 NOT NULL DEFAULT GETDATE()
);
```

## Development Guidelines

### ✅ Doğru Yaklaşımlar
1. **Service Separation:** Logo services pure, business services coordinate
2. **Inline Logging:** JSON creation directly in business services
3. **Direct Table Creation:** No migrations during development
4. **Turkish Messages:** All user-facing messages in Turkish
5. **Multi-Tenant Aware:** Always use `veritabaniId` parameter

### ❌ Yanlış Yaklaşımlar
1. **Logo Services'de Loglama:** Logo services should not log to application database
2. **Utility Functions:** Avoid utility functions for business logic
3. **Migration Files:** Don't create separate migration files during development
4. **Mixed Responsibilities:** Don't mix Logo operations with application database operations

Bu mimari rehber, VNS Mizan projesinin tutarlı ve maintainable geliştirilmesi için temel prensipleri belirlemektedir.
