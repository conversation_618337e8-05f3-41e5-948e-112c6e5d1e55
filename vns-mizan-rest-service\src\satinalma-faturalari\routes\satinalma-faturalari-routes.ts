import type { RequestHandler } from 'express'
import { Router } from 'express'
import { z } from 'zod'
import SatinalmaFaturalariService from '../services/satinalma-faturalari-service.ts'

const router = Router()

// Date format regex (YYYY-MM-DD)
const dateFormatRegex = /^\d{4}-\d{2}-\d{2}$/

// Time format regex (HH:MM:SS) - Use non-capturing groups
const timeFormatRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/

/**
 * Satınalma Faturası request validation schema
 */
export const satinalmaFaturalariSchema = z.object({
  veritabani_id: z
    .string({
      required_error: 'Veritabanı ID zorunludur',
    })
    .uuid('Geçerli bir UUID formatında veritabanı ID girilmelidir'),

  // Logo kullanıcı bilgileri
  logo: z.object({
    kullanici_adi: z.string().describe('Logo kullanıcı adı'),
    sifre: z.string().describe('Logo kullanıcı şifresi'),
  }).optional().describe('Logo kullanıcı bilgileri. Eğer belirtilmezse config.json\'daki kullanıcı bilgileri kullanılır'),

  fatura_turu: z
    .number({
      required_error: 'Fatura türü zorunludur',
    })
    .int('Fatura türü tam sayı olmalıdır')
    .refine(val => val === 4, {
      message: 'Fatura türü 4 (Alınan Hizmet Faturası) olmalıdır',
    })
    .describe('Fatura türü kodu (4=Alınan Hizmet Faturası)'),

  fatura_no: z
    .string()
    .max(16, 'Fatura numarası en fazla 16 karakter olabilir')
    .optional()
    .describe('Fatura numarası. Boş bırakılırsa veya "~" girilirse Logo tarafından otomatik atanır'),

  tarihi: z
    .string({
      required_error: 'Fatura tarihi zorunludur',
    })
    .regex(dateFormatRegex, 'Fatura tarihi formatı YYYY-MM-DD olmalıdır')
    .describe('Fatura tarihi (YYYY-MM-DD)'),

  saati: z
    .string({
      required_error: 'Fatura saati zorunludur',
    })
    .regex(timeFormatRegex, 'Fatura saati formatı HH:MM:SS olmalıdır')
    .describe('Fatura saati (HH:MM:SS)'),

  belge_no: z
    .string()
    .max(32, 'Belge numarası en fazla 32 karakter olabilir')
    .optional()
    .describe('Belge numarası'),

  ozel_kod: z
    .string()
    .max(10, 'Özel kod en fazla 10 karakter olabilir')
    .optional()
    .describe('Özel kod'),

  cari_kodu: z
    .string()
    .max(16, 'Cari kodu en fazla 16 karakter olabilir')
    .optional()
    .describe('Cari kodu'),

  ambar_kodu: z
    .number()
    .int('Ambar kodu tam sayı olmalıdır')
    .optional()
    .describe('Ambar kodu'),

  fabrika_kodu: z
    .number()
    .int('Fabrika kodu tam sayı olmalıdır')
    .optional()
    .describe('Fabrika kodu'),

  aciklama: z
    .string()
    .max(1800, 'Açıklama en fazla 1800 karakter olabilir')
    .optional()
    .describe('Açıklama'),

  doviz_kuru: z
    .number()
    .optional()
    .describe('Döviz kuru'),

  isyeri_kodu: z
    .number()
    .int('İşyeri kodu tam sayı olmalıdır')
    .optional()
    .describe('İşyeri kodu'),

  bolum_kodu: z
    .number()
    .int('Bölüm kodu tam sayı olmalıdır')
    .optional()
    .describe('Bölüm kodu'),

  odeme_kodu: z
    .string()
    .max(16, 'Ödeme kodu en fazla 16 karakter olabilir')
    .optional()
    .describe('Ödeme kodu'),

  proje_kodu: z
    .string()
    .max(100, 'Proje kodu en fazla 100 karakter olabilir')
    .optional()
    .describe('Proje kodu'),

  belge_tarihi: z
    .string()
    .regex(dateFormatRegex, 'Belge tarihi formatı YYYY-MM-DD olmalıdır')
    .optional()
    .describe('Belge tarihi (YYYY-MM-DD)'),

  fatura_satirlari: z
    .array(
      z.object({
        satir_turu: z
          .number({
            required_error: 'Satır türü zorunludur',
          })
          .int('Satır türü tam sayı olmalıdır')
          .describe('Satır türü (0=Malzeme, 1=Hizmet, 2=İndirim, 3=Masraf, 4=Hizmet)'),

        malzeme_kodu: z
          .string()
          .max(16, 'Malzeme kodu en fazla 16 karakter olabilir')
          .optional()
          .describe('Malzeme kodu'),

        ambar_kodu: z
          .number()
          .int('Ambar kodu tam sayı olmalıdır')
          .optional()
          .describe('Ambar kodu'),

        fabrika_kodu: z
          .number()
          .int('Fabrika kodu tam sayı olmalıdır')
          .optional()
          .describe('Fabrika kodu'),

        hareket_ozel_kodu: z
          .string()
          .max(17, 'Hareket özel kodu en fazla 17 karakter olabilir')
          .optional()
          .describe('Hareket özel kodu'),

        miktar: z
          .number()
          .optional()
          .describe('Miktar'),

        birim_fiyat: z
          .number()
          .optional()
          .describe('Birim fiyat'),

        aciklama: z
          .string()
          .max(250, 'Açıklama en fazla 250 karakter olabilir')
          .optional()
          .describe('Açıklama'),

        birim_kodu: z
          .string()
          .max(10, 'Birim kodu en fazla 10 karakter olabilir')
          .optional()
          .describe('Birim kodu'),

        kdv_orani: z
          .number()
          .optional()
          .describe('KDV oranı'),

        proje_kodu: z
          .string()
          .max(100, 'Proje kodu en fazla 100 karakter olabilir')
          .optional()
          .describe('Proje kodu'),

        dovizli_birim_fiyat: z
          .number()
          .optional()
          .describe('Dövizli birim fiyat'),

        tevkifat_yapilabilir: z
          .number()
          .int('Tevkifat yapılabilir değeri tam sayı olmalıdır')
          .optional()
          .describe('Tevkifat yapılabilir (0=Hayır, 1=Evet)'),

        tevkifat_payi1: z
          .number()
          .int('Tevkifat payı 1 tam sayı olmalıdır')
          .optional()
          .describe('Tevkifat payı 1'),

        tevkifat_payi2: z
          .number()
          .int('Tevkifat payı 2 tam sayı olmalıdır')
          .optional()
          .describe('Tevkifat payı 2'),

        tevkifat_kodu: z
          .string()
          .max(16, 'Tevkifat kodu en fazla 16 karakter olabilir')
          .optional()
          .describe('Tevkifat kodu'),

        tevkifat_aciklamasi: z
          .string()
          .max(250, 'Tevkifat açıklaması en fazla 250 karakter olabilir')
          .optional()
          .describe('Tevkifat açıklaması'),

        masraf_merkezi1: z
          .string()
          .max(25, 'Masraf merkezi 1 en fazla 25 karakter olabilir')
          .optional()
          .describe('Masraf merkezi 1'),

        masraf_merkezi3: z
          .string()
          .max(25, 'Masraf merkezi 3 en fazla 25 karakter olabilir')
          .optional()
          .describe('Masraf merkezi 3'),

        masraf_merkezi4: z
          .string()
          .max(25, 'Masraf merkezi 4 en fazla 25 karakter olabilir')
          .optional()
          .describe('Masraf merkezi 4'),
      }),
    )
    .min(1, 'En az bir fatura satırı girilmelidir')
    .max(9999, 'En fazla 9999 fatura satırı girilebilir')
    .describe('Fatura satırları'),
})

/**
 * @openapi
 * /satinalma-faturalari:
 *   post:
 *     tags: [Satınalma Faturaları]
 *     summary: Yeni satınalma faturası oluşturur
 *     security:
 *       - sessionAuth: []
 *     description: |
 *       Logo sisteminde yeni bir satınalma faturası oluşturur. Fatura başlık bilgileri ve kalem detayları eklenir.
 *
 *       Bu modül hem REST API hem de doğrudan SQL entegrasyonunu desteklemektedir:
 *
 *       - **REST API Entegrasyonu** (`use_rest=true`): Logo REST API'si kullanılarak fatura oluşturulur
 *       - **Doğrudan SQL Entegrasyonu** (`use_rest=false`): Logo veritabanı tablolarına doğrudan SQL ile fatura eklenir
 *
 *       Entegrasyon türü, veritabanı yapılandırmasındaki `use_rest` ayarına göre otomatik olarak belirlenir.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - veritabani_id
 *               - fatura_turu
 *               - tarihi
 *               - saati
 *               - fatura_satirlari
 *             properties:
 *               veritabani_id:
 *                 type: string
 *                 format: uuid
 *                 description: Logo veritabanı ID'si
 *               logo:
 *                 type: object
 *                 description: Logo kullanıcı bilgileri. Eğer belirtilmezse config.json'daki kullanıcı bilgileri kullanılır
 *                 properties:
 *                   kullanici_adi:
 *                     type: string
 *                     description: Logo kullanıcı adı
 *                   sifre:
 *                     type: string
 *                     description: Logo kullanıcı şifresi
 *               fatura_turu:
 *                 type: integer
 *                 enum: [4]
 *                 description: Fatura türü kodu (4=Alınan Hizmet Faturası)
 *               fatura_no:
 *                 type: string
 *                 maxLength: 16
 *                 description: Fatura numarası. Boş bırakılırsa veya "~" girilirse Logo tarafından otomatik atanır
 *               tarihi:
 *                 type: string
 *                 format: date
 *                 description: Fatura tarihi (YYYY-MM-DD)
 *               saati:
 *                 type: string
 *                 pattern: ^([01]\d|2[0-3]):[0-5]\d:[0-5]\d$
 *                 description: Fatura saati (HH:MM:SS)
 *               belge_no:
 *                 type: string
 *                 maxLength: 32
 *                 description: Belge numarası
 *               ozel_kod:
 *                 type: string
 *                 maxLength: 10
 *                 description: Özel kod
 *               cari_kodu:
 *                 type: string
 *                 maxLength: 16
 *                 description: Cari kodu
 *               ambar_kodu:
 *                 type: integer
 *                 description: Ambar kodu
 *               fabrika_kodu:
 *                 type: integer
 *                 description: Fabrika kodu
 *               aciklama:
 *                 type: string
 *                 maxLength: 1800
 *                 description: Açıklama
 *               doviz_kuru:
 *                 type: number
 *                 description: Döviz kuru
 *               isyeri_kodu:
 *                 type: integer
 *                 description: İşyeri kodu
 *               bolum_kodu:
 *                 type: integer
 *                 description: Bölüm kodu
 *               odeme_kodu:
 *                 type: string
 *                 maxLength: 16
 *                 description: Ödeme kodu
 *               proje_kodu:
 *                 type: string
 *                 maxLength: 100
 *                 description: Proje kodu
 *               belge_tarihi:
 *                 type: string
 *                 format: date
 *                 description: Belge tarihi (YYYY-MM-DD)
 *               fatura_satirlari:
 *                 type: array
 *                 minItems: 1
 *                 maxItems: 9999
 *                 description: Fatura satırları
 *                 items:
 *                   type: object
 *                   required:
 *                     - satir_turu
 *                   properties:
 *                     satir_turu:
 *                       type: integer
 *                       description: Satır türü (0=Malzeme, 1=Hizmet, 2=İndirim, 3=Masraf, 4=Hizmet)
 *                     malzeme_kodu:
 *                       type: string
 *                       maxLength: 16
 *                       description: Malzeme kodu
 *                     ambar_kodu:
 *                       type: integer
 *                       description: Ambar kodu
 *                     fabrika_kodu:
 *                       type: integer
 *                       description: Fabrika kodu
 *                     hareket_ozel_kodu:
 *                       type: string
 *                       maxLength: 17
 *                       description: Hareket özel kodu
 *                     miktar:
 *                       type: number
 *                       description: Miktar
 *                     birim_fiyat:
 *                       type: number
 *                       description: Birim fiyat
 *                     aciklama:
 *                       type: string
 *                       maxLength: 250
 *                       description: Açıklama
 *                     birim_kodu:
 *                       type: string
 *                       maxLength: 10
 *                       description: Birim kodu
 *                     kdv_orani:
 *                       type: number
 *                       description: KDV oranı
 *                     proje_kodu:
 *                       type: string
 *                       maxLength: 100
 *                       description: Proje kodu
 *                     dovizli_birim_fiyat:
 *                       type: number
 *                       description: Dövizli birim fiyat
 *                     tevkifat_yapilabilir:
 *                       type: integer
 *                       enum: [0, 1]
 *                       description: Tevkifat yapılabilir (0=Hayır, 1=Evet)
 *                     tevkifat_payi1:
 *                       type: integer
 *                       description: Tevkifat payı 1
 *                     tevkifat_payi2:
 *                       type: integer
 *                       description: Tevkifat payı 2
 *                     tevkifat_kodu:
 *                       type: string
 *                       maxLength: 16
 *                       description: Tevkifat kodu
 *                     tevkifat_aciklamasi:
 *                       type: string
 *                       maxLength: 250
 *                       description: Tevkifat açıklaması
 *                     masraf_merkezi1:
 *                       type: string
 *                       maxLength: 25
 *                       description: Masraf merkezi 1
 *                     masraf_merkezi3:
 *                       type: string
 *                       maxLength: 25
 *                       description: Masraf merkezi 3
 *                     masraf_merkezi4:
 *                       type: string
 *                       maxLength: 25
 *                       description: Masraf merkezi 4
 *     responses:
 *       200:
 *         description: Başarılı
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [success, error]
 *                   description: İşlem durumu
 *                 data:
 *                   type: object
 *                   description: Başarılı işlem sonucu
 *                   properties:
 *                     message:
 *                       type: string
 *                       description: İşlem mesajı
 *                     id:
 *                       type: integer
 *                       description: Oluşturulan fatura ID'si
 *                     useRest:
 *                       type: boolean
 *                       description: REST API kullanıldı mı?
 *                     ficheNo:
 *                       type: string
 *                       description: Logo fatura numarası
 *                 error:
 *                   type: string
 *                   description: Hata mesajı (varsa)
 *                 logoRef:
 *                   type: integer
 *                   description: Logo referans numarası (INTERNAL_REFERENCE)
 *                 ficheNo:
 *                   type: string
 *                   description: Logo fatura numarası
 *       400:
 *         description: Geçersiz istek
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Hata mesajı
 *       500:
 *         description: Sunucu hatası
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Hata mesajı
 *                 veritabani_id:
 *                   type: string
 *                   description: Veritabanı ID'si
 */
const postSatinalmaFaturalariHandler: RequestHandler = async (req, res, _next) => {
  try {
    // Validate request body against schema
    const result = satinalmaFaturalariSchema.safeParse(req.body)

    if (!result.success) {
      res.status(400).json({
        message: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', '),
      })
      return
    }

    // Convert request data to Logo format
    const logoApiData = SatinalmaFaturalariService.convertRequestToLogoFormat(result.data)

    // Extract veritabani_id from the data
    const { veritabani_id } = result.data

    // Pass original payload to the service
    const response = await SatinalmaFaturalariService.handleCreateInvoice({
      invoiceData: logoApiData,
      veritabaniId: veritabani_id,
      requestPayload: result.data, // Pass the validated request data
    })

    // Format the response according to the new structure
    if (response.status === 'success') {
      // Format date to DD.MM.YYYY
      const date = result.data.tarihi
      const formattedDate = date ? new Date(date).toLocaleDateString('tr-TR') : undefined

      res.status(200).json({
        message: response.data?.message || 'Satınalma faturası başarıyla oluşturuldu',
        data: {
          fatura_no: response.ficheNo,
          fatura_tarihi: formattedDate,
          logicalref: response.logoRef,
          veritabani_id,
        },
      })
    }
    else {
      // Error response
      res.status(400).json({
        message: response.error || 'Satınalma faturası oluşturulurken bir hata oluştu',
        veritabani_id,
      })
    }
  }
  catch (error) {
    console.error('Satınalma faturası kaydedilirken hata oluştu:', error)
    // Try to get veritabani_id from request body if available
    const veritabani_id = req.body?.veritabani_id || 'unknown'
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Bir hata oluştu.',
      veritabani_id,
    })
  }
}

router.post('/', postSatinalmaFaturalariHandler)

export default router
