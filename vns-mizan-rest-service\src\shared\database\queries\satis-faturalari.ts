/**
 * SQL queries for 'SatisFaturalari' module initialization and operations
 */
export const query = `
-- Table: SatisFaturalari
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatisFaturalari' and xtype='U')
BEGIN
    CREATE TABLE SatisFaturalari (
        id int IDENTITY(1,1) PRIMARY KEY,
        fatura_turu int NOT NULL,
        fatura_no varchar(16) NOT NULL DEFAULT '~',
        tarihi date NOT NULL,
        saati varchar(8) NOT NULL,
        belge_no varchar(32) NULL,
        ozel_kod varchar(10) NULL,
        cari_kodu varchar(16) NULL,
        ambar_kodu int NULL,
        fabrika_kodu int NULL,
        aciklama varchar(1800) NULL,
        doviz_kuru float NULL,
        isyeri_kodu int NULL,
        bolum_kodu int NULL,
        satis_elemani varchar(24) NULL,
        proje_kodu varchar(100) NULL,
        belge_tarihi date NULL,
        irsaliye_no varchar(16) NULL,
        irsaliye_tarihi date NULL,
        irsaliye_saati varchar(8) NULL,
        logo_fatura_no varchar(16) NULL,
        logo_irsaliye_no varchar(16) NULL,
        logo_fatura_logicalref int NULL,
        logo_irsaliye_logicalref int NULL,
        logo_kullanici_adi varchar(50) NULL,
        error varchar(max) NULL,
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        veritabani_id VARCHAR(37),
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'SatisFaturalari tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'SatisFaturalari tablosu zaten mevcut';
END;

-- Table: SatisFaturalariSatirlari
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatisFaturalariSatirlari' and xtype='U')
BEGIN
    CREATE TABLE SatisFaturalariSatirlari (
        id int IDENTITY(1,1) PRIMARY KEY,
        satis_fatura_id int NOT NULL,
        satir_turu int NOT NULL,
        malzeme_kodu varchar(16) NULL,
        ambar_kodu int NULL,
        fabrika_kodu int NULL,
        hareket_ozel_kodu varchar(17) NULL,
        miktar float NULL,
        indirim_tutari float NULL,
        birim_fiyat float NULL,
        para_birimi varchar(10) NULL,
        dovizli_birim_fiyat float NULL,
        doviz_kuru float NULL,
        aciklama varchar(250) NULL,
        indirim_orani float NULL,
        birim_kodu varchar(10) NULL,
        kdv_orani float NULL,
        satis_elemani varchar(24) NULL,
        proje_kodu varchar(100) NULL,
        logo_rest_data VARCHAR(MAX) NULL,
        logo_sql_data VARCHAR(MAX) NULL,
        createdAt datetime NOT NULL DEFAULT GETDATE()
    );
    PRINT 'SatisFaturalariSatirlari tablosu başarıyla oluşturuldu';
END
ELSE
BEGIN
    PRINT 'SatisFaturalariSatirlari tablosu zaten mevcut';
END;

BEGIN
    -- Table: LogoSatisFaturalari
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoSatisFaturalari' and xtype='U')
    BEGIN
        CREATE TABLE LogoSatisFaturalari (
            id int IDENTITY(1,1) PRIMARY KEY,
            TYPE int NOT NULL,
            NUMBER varchar(16) NOT NULL,
            DATE date NOT NULL,
            TIME int NOT NULL,
            DOC_NUMBER varchar(32) NULL,
            AUXIL_CODE varchar(10) NULL,
            ARP_CODE varchar(16) NULL,
            SOURCE_WH int NULL,
            SOURCE_COST_GRP int NULL,
            FACTORY int NULL,
            NOTES1 varchar(300) NULL,
            NOTES2 varchar(300) NULL,
            NOTES3 varchar(300) NULL,
            NOTES4 varchar(300) NULL,
            NOTES5 varchar(300) NULL,
            NOTES6 varchar(300) NULL,
            CURR_INVOICE int NULL,
            TC_XRATE float NULL,
            RC_XRATE float NULL,
            DIVISION int NULL,
            DEPARTMENT int NULL,
            SALESMAN_CODE varchar(24) NULL,
            CURRSEL_TOTALS int NULL,
            CURRSEL_DETAILS int NULL,
            PROJECT_CODE varchar(100) NULL,
            AFFECT_RISK int NOT NULL DEFAULT 1,
            DOC_DATE date NULL,
            PROFILE_ID int NULL,
            EINSTEAD_OF_DISPATCH int NULL,
            logo_fatura_no varchar(16) NULL,
            logo_fatura_logicalref int NULL,
            logo_kullanici_adi varchar(50) NULL,
            error varchar(max) NULL,
            veritabani_id VARCHAR(37),
            createdAt datetime NOT NULL DEFAULT GETDATE()
        );
        PRINT 'LogoSatisFaturalari tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'LogoSatisFaturalari tablosu zaten mevcut';
    END;

    -- Table: LogoSatisFaturalariIrsaliyesi
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoSatisFaturalariIrsaliyesi' and xtype='U')
    BEGIN
        CREATE TABLE LogoSatisFaturalariIrsaliyesi (
            id int IDENTITY(1,1) PRIMARY KEY,
            satis_fatura_id int NOT NULL,
            TYPE int NOT NULL,
            NUMBER varchar(16) NOT NULL DEFAULT '~',
            DATE date NOT NULL,
            TIME int NOT NULL,
            logo_irsaliye_no varchar(16) NULL,
            logo_irsaliye_logicalref int NULL,
            error varchar(max) NULL,
            veritabani_id VARCHAR(37),
            createdAt datetime NOT NULL DEFAULT GETDATE()
        );
        PRINT 'LogoSatisFaturalariIrsaliyesi tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'LogoSatisFaturalariIrsaliyesi tablosu zaten mevcut';
    END;

    -- Table: LogoSatisFaturalariSatirlari
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoSatisFaturalariSatirlari' and xtype='U')
    BEGIN
        CREATE TABLE LogoSatisFaturalariSatirlari (
            id int IDENTITY(1,1) PRIMARY KEY,
            satis_fatura_id int NOT NULL,
            TYPE int NOT NULL,
            MASTER_CODE varchar(16) NULL,
            DISCEXP_CALC int NULL,
            SOURCEINDEX int NULL,
            SOURCE_COST_GRP int NULL,
            FACTORY int NULL,
            AUXIL_CODE varchar(10) NULL,
            QUANTITY float NULL,
            TOTAL float NULL,
            PRICE float NULL,
            EDT_CURR int NULL,
            EDT_PRICE float NULL,
            TC_XRATE float NULL,
            DESCRIPTION varchar(250) NULL,
            DISCOUNT_RATE float NULL,
            UNIT_CODE varchar(10) NULL,
            VAT_INCLUDED int NULL,
            VAT_RATE float NULL,
            BILLED int NULL,
            SALEMANCODE varchar(24) NULL,
            PROJECT_CODE varchar(100) NULL,
            AFFECT_RISK int NULL,
            error varchar(max) NULL,
            createdAt datetime NOT NULL DEFAULT GETDATE()
        );
        PRINT 'LogoSatisFaturalariSatirlari tablosu başarıyla oluşturuldu';
    END
    ELSE
    BEGIN
        PRINT 'LogoSatisFaturalariSatirlari tablosu zaten mevcut';
    END;
END;

-- Check primary table creations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SatisFaturalari' and xtype='U')
    THROW 50008, 'SatisFaturalari tablosu düzgün oluşturulmadı', 1;
IF  NOT EXISTS (SELECT * FROM sysobjects WHERE name='LogoSatisFaturalari' and xtype='U')
    THROW 50009, 'LogoSatisFaturalari tablosu düzgün oluşturulmadı', 1;
`
